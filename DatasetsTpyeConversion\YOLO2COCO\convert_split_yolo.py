"""
已分割YOLO数据集转COCO格式工具

适用于已经分割好的YOLO数据集，包含独立的train/val文件夹
输入结构：
yolo_dataset/
├── images/
│   ├── train/
│   └── val/
└── labels/
    ├── train/
    └── val/

输出结构：
coco_dataset/
├── images/
│   ├── train/
│   └── val/
├── annotations/
│   ├── train.json
│   └── val.json
└── dataset_info.json

修改配置参数后直接运行即可
"""

from yolo_split2coco import YoloSplitToCocoDataset, convert_split_yolo_to_coco
import os


def main():
    """主函数 - 转换已分割的YOLO数据集"""
    
    # ==================== 配置参数 ====================
    
    # YOLO数据集根目录（包含images和labels文件夹）
    YOLO_ROOT_DIR = r"C:\Users\<USER>\Desktop\datasets\yolo_dataset"
    
    # 输出COCO数据集目录
    OUTPUT_DIR = r"C:\Users\<USER>\Desktop\datasets\coco_dataset"
    
    # 类别名称列表（按照YOLO标注文件中的类别ID顺序）
    CLASS_NAMES = [
        "kerf"
        # 根据您的数据集添加更多类别...
    ]
    
    # 任务类型
    TASK_TYPE = "detection"  # "detection" 或 "segmentation"
    
    # 要处理的数据集分割（根据您的实际情况调整）
    SPLITS = ["train", "val"]  # 可以包含 "train", "val", "test" 等
    
    # ==================== 执行转换 ====================
    
    print("=" * 80)
    print("已分割YOLO数据集转COCO格式转换工具")
    print("=" * 80)
    
    # 显示配置信息
    print("📋 配置信息:")
    print(f"   YOLO数据集目录: {YOLO_ROOT_DIR}")
    print(f"   输出COCO目录: {OUTPUT_DIR}")
    print(f"   类别数量: {len(CLASS_NAMES)}")
    print(f"   类别列表: {CLASS_NAMES}")
    print(f"   任务类型: {TASK_TYPE}")
    print(f"   处理分割: {SPLITS}")
    print("-" * 80)
    
    # 验证YOLO数据集结构
    print("🔍 验证YOLO数据集结构...")
    
    yolo_images_dir = os.path.join(YOLO_ROOT_DIR, 'images')
    yolo_labels_dir = os.path.join(YOLO_ROOT_DIR, 'labels')
    
    if not os.path.exists(YOLO_ROOT_DIR):
        print(f"❌ 错误：YOLO数据集根目录不存在: {YOLO_ROOT_DIR}")
        return
    
    if not os.path.exists(yolo_images_dir):
        print(f"❌ 错误：images文件夹不存在: {yolo_images_dir}")
        return
    
    if not os.path.exists(yolo_labels_dir):
        print(f"❌ 错误：labels文件夹不存在: {yolo_labels_dir}")
        return
    
    # 检查各个分割
    missing_splits = []
    for split in SPLITS:
        split_images = os.path.join(yolo_images_dir, split)
        split_labels = os.path.join(yolo_labels_dir, split)
        
        if not os.path.exists(split_images):
            missing_splits.append(f"images/{split}")
        if not os.path.exists(split_labels):
            missing_splits.append(f"labels/{split}")
    
    if missing_splits:
        print(f"❌ 错误：缺少以下分割目录: {missing_splits}")
        return
    
    # 显示数据集统计
    print("📊 数据集统计:")
    for split in SPLITS:
        split_images_dir = os.path.join(yolo_images_dir, split)
        split_labels_dir = os.path.join(yolo_labels_dir, split)
        
        image_count = len([f for f in os.listdir(split_images_dir) 
                          if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))])
        label_count = len([f for f in os.listdir(split_labels_dir) 
                          if f.endswith('.txt')])
        
        print(f"   {split:>5} 集: {image_count:>4} 张图片, {label_count:>4} 个标注文件")
    
    # 检查输出目录
    if os.path.exists(OUTPUT_DIR):
        response = input(f"\\n⚠️  输出目录已存在: {OUTPUT_DIR}\\n是否继续？(y/n): ")
        if response.lower() != 'y':
            print("转换已取消")
            return
    
    print("\\n🚀 开始转换...")
    
    try:
        # 创建转换器并执行转换
        converter = YoloSplitToCocoDataset(YOLO_ROOT_DIR, OUTPUT_DIR, CLASS_NAMES)
        results = converter.convert(TASK_TYPE, SPLITS)
        
        # 显示转换结果
        print("\\n✅ 转换成功完成！")
        print("\\n📊 转换统计:")
        total_images = 0
        total_annotations = 0
        
        for split_name, coco_data in results.items():
            images_count = len(coco_data['images'])
            annotations_count = len(coco_data['annotations'])
            total_images += images_count
            total_annotations += annotations_count
            print(f"   {split_name:>5} 集: {images_count:>4} 张图片, {annotations_count:>5} 个标注")
        
        print(f"   {'总计':>5}: {total_images:>4} 张图片, {total_annotations:>5} 个标注")
        
        # 显示生成的文件结构
        print("\\n📁 生成的COCO数据集结构:")
        print(f"{OUTPUT_DIR}/")
        print("├── images/")
        for split_name in results.keys():
            print(f"│   ├── {split_name}/")
        print("├── annotations/")
        for split_name in results.keys():
            print(f"│   ├── {split_name}.json")
        print("└── dataset_info.json")
        
        print(f"\\n🎉 COCO数据集已成功创建在: {OUTPUT_DIR}")
        
        # 验证生成的数据集
        validate_coco_dataset(OUTPUT_DIR)
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()


def validate_coco_dataset(coco_dir):
    """验证生成的COCO数据集"""
    print("\\n🔍 验证生成的COCO数据集...")
    
    # 检查目录结构
    required_dirs = ['images', 'annotations']
    for dir_name in required_dirs:
        dir_path = os.path.join(coco_dir, dir_name)
        if os.path.exists(dir_path):
            print(f"   ✅ {dir_name}/ 目录存在")
        else:
            print(f"   ❌ {dir_name}/ 目录缺失")
    
    # 检查标注文件
    annotations_dir = os.path.join(coco_dir, 'annotations')
    if os.path.exists(annotations_dir):
        json_files = [f for f in os.listdir(annotations_dir) if f.endswith('.json')]
        print(f"   📄 标注文件: {json_files}")
        
        # 验证JSON文件格式
        for json_file in json_files:
            json_path = os.path.join(annotations_dir, json_file)
            try:
                import json
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"   ✅ {json_file}: {len(data['images'])} 张图片, {len(data['annotations'])} 个标注")
            except Exception as e:
                print(f"   ❌ {json_file}: JSON格式错误 - {e}")
    
    # 检查数据集信息文件
    info_file = os.path.join(coco_dir, 'dataset_info.json')
    if os.path.exists(info_file):
        print("   ✅ dataset_info.json 存在")
    else:
        print("   ❌ dataset_info.json 缺失")


def quick_detection_example():
    """快速检测任务转换示例"""
    try:
        results = convert_split_yolo_to_coco(
            yolo_root_dir=r"E:\my_yolo_dataset",
            output_dir=r"E:\my_coco_dataset",
            class_names=["person", "car"],
            task_type="detection",
            splits=["train", "val"]
        )
        print("快速检测转换完成！")
        return results
    except Exception as e:
        print(f"快速转换失败: {e}")


def quick_segmentation_example():
    """快速分割任务转换示例"""
    try:
        results = convert_split_yolo_to_coco(
            yolo_root_dir=r"E:\my_yolo_seg_dataset",
            output_dir=r"E:\my_coco_seg_dataset",
            class_names=["person", "car"],
            task_type="segmentation",
            splits=["train", "val"]
        )
        print("快速分割转换完成！")
        return results
    except Exception as e:
        print(f"快速转换失败: {e}")


def check_yolo_dataset_structure(yolo_root_dir):
    """检查YOLO数据集结构"""
    print(f"检查YOLO数据集结构: {yolo_root_dir}")
    
    if not os.path.exists(yolo_root_dir):
        print(f"❌ 根目录不存在: {yolo_root_dir}")
        return False
    
    images_dir = os.path.join(yolo_root_dir, 'images')
    labels_dir = os.path.join(yolo_root_dir, 'labels')
    
    print(f"📁 {yolo_root_dir}/")
    
    if os.path.exists(images_dir):
        print("├── images/")
        for item in os.listdir(images_dir):
            item_path = os.path.join(images_dir, item)
            if os.path.isdir(item_path):
                count = len([f for f in os.listdir(item_path) 
                           if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))])
                print(f"│   ├── {item}/ ({count} 张图片)")
    else:
        print("├── images/ ❌ 不存在")
    
    if os.path.exists(labels_dir):
        print("└── labels/")
        for item in os.listdir(labels_dir):
            item_path = os.path.join(labels_dir, item)
            if os.path.isdir(item_path):
                count = len([f for f in os.listdir(item_path) if f.endswith('.txt')])
                print(f"    ├── {item}/ ({count} 个标注文件)")
    else:
        print("└── labels/ ❌ 不存在")
    
    return True


if __name__ == "__main__":
    # 运行主转换程序
    main()
    
    # 或者检查数据集结构
    # check_yolo_dataset_structure(r"E:\datasets\yolo_dataset")
    
    # 或者运行快速示例
    # quick_detection_example()
    # quick_segmentation_example()