#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
DLL文件替换工具
用于搜索并替换系统中的指定DLL文件
支持备份、版本检查、批量替换等功能
"""

import os
import sys
import shutil
import argparse
import logging
import json
from pathlib import Path
from datetime import datetime
import win32api
import win32file
import psutil
import hashlib


class DLLReplacer:
    """DLL文件替换器主类"""
    
    def __init__(self, config):
        """
        初始化DLL替换器
        
        参数:
            config: 配置字典，包含所有替换参数
        """
        self.config = config
        self.logger = self._setup_logger()
        self.backup_index = []
        self.operation_stats = {
            'files_found': 0,
            'files_backed_up': 0,
            'files_replaced': 0,
            'files_skipped': 0,
            'errors': 0
        }
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('DLLReplacer')
        logger.setLevel(logging.DEBUG if self.config.get('verbose') else logging.INFO)
        
        # 创建日志目录
        log_dir = Path(self.config.get('log_dir', './logs'))
        log_dir.mkdir(exist_ok=True)
        
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 文件处理器
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = log_dir / f'dll_replacer_{timestamp}.log'
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        return logger
    
    def get_file_version(self, file_path):
        """
        获取文件版本信息
        
        参数:
            file_path: 文件路径
            
        返回:
            版本信息字典或None
        """
        try:
            info = win32api.GetFileVersionInfo(file_path, "\\")
            ms = info['FileVersionMS']
            ls = info['FileVersionLS']
            version = f"{win32api.HIWORD(ms)}.{win32api.LOWORD(ms)}.{win32api.HIWORD(ls)}.{win32api.LOWORD(ls)}"
            return {
                'version': version,
                'description': win32api.GetFileVersionInfo(file_path, "\\StringFileInfo\\040904B0\\FileDescription"),
                'company': win32api.GetFileVersionInfo(file_path, "\\StringFileInfo\\040904B0\\CompanyName")
            }
        except Exception as e:
            self.logger.debug(f"无法获取文件版本信息 {file_path}: {str(e)}")
            return None
    
    def get_file_hash(self, file_path):
        """
        计算文件MD5哈希值
        
        参数:
            file_path: 文件路径
            
        返回:
            MD5哈希值字符串
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.logger.error(f"计算文件哈希失败 {file_path}: {str(e)}")
            return None
    
    def is_file_in_use(self, file_path):
        """
        检查文件是否被其他进程占用
        
        参数:
            file_path: 文件路径
            
        返回:
            True如果被占用，False如果可用
        """
        try:
            # 尝试以独占模式打开文件
            handle = win32file.CreateFile(
                file_path,
                win32file.GENERIC_READ | win32file.GENERIC_WRITE,
                0,  # 不共享
                None,
                win32file.OPEN_EXISTING,
                win32file.FILE_ATTRIBUTE_NORMAL,
                None
            )
            win32file.CloseHandle(handle)
            return False
        except:
            return True
    
    def find_processes_using_file(self, file_path):
        """
        查找正在使用指定文件的进程
        
        参数:
            file_path: 文件路径
            
        返回:
            使用该文件的进程列表
        """
        processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    for item in proc.memory_maps():
                        if item.path and os.path.samefile(item.path, file_path):
                            processes.append({
                                'pid': proc.info['pid'],
                                'name': proc.info['name']
                            })
                            break
                except (psutil.NoSuchProcess, psutil.AccessDenied, OSError):
                    continue
        except Exception as e:
            self.logger.debug(f"查找进程时出错: {str(e)}")
        
        return processes
    
    def search_dll_files(self):
        """
        搜索目标DLL文件
        
        返回:
            找到的DLL文件路径列表
        """
        dll_files = []
        target_name = self.config['target_name']
        search_paths = self.config['search_paths']
        exclude_paths = self.config.get('exclude_paths', [])
        
        self.logger.info(f"开始搜索 {target_name} 文件...")
        
        for search_path in search_paths:
            if not os.path.exists(search_path):
                self.logger.warning(f"搜索路径不存在: {search_path}")
                continue
            
            self.logger.info(f"正在搜索路径: {search_path}")
            
            try:
                for root, dirs, files in os.walk(search_path):
                    # 检查是否在排除路径中
                    if any(root.startswith(exclude_path) for exclude_path in exclude_paths):
                        continue
                    
                    for file in files:
                        if file.lower() == target_name.lower():
                            file_path = os.path.join(root, file)
                            dll_files.append(file_path)
                            self.logger.debug(f"找到文件: {file_path}")
            
            except PermissionError as e:
                self.logger.warning(f"权限不足，无法访问: {root}")
            except Exception as e:
                self.logger.error(f"搜索过程中出错: {str(e)}")
        
        self.operation_stats['files_found'] = len(dll_files)
        self.logger.info(f"搜索完成，共找到 {len(dll_files)} 个文件")
        
        return dll_files
    
    def create_backup(self, file_path):
        """
        创建文件备份
        
        参数:
            file_path: 要备份的文件路径
            
        返回:
            备份文件路径或None
        """
        try:
            backup_dir = Path(self.config['backup_dir'])
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 保持原始目录结构
            relative_path = os.path.relpath(file_path, '/')
            backup_path = backup_dir / relative_path
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 添加时间戳避免冲突
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"{backup_path.stem}_{timestamp}{backup_path.suffix}"
            final_backup_path = backup_path.parent / backup_name
            
            shutil.copy2(file_path, final_backup_path)
            
            # 记录备份信息
            backup_info = {
                'original_path': file_path,
                'backup_path': str(final_backup_path),
                'timestamp': timestamp,
                'original_hash': self.get_file_hash(file_path),
                'original_version': self.get_file_version(file_path)
            }
            self.backup_index.append(backup_info)
            
            self.operation_stats['files_backed_up'] += 1
            self.logger.info(f"已备份: {file_path} -> {final_backup_path}")
            
            return str(final_backup_path)
            
        except Exception as e:
            self.logger.error(f"备份文件失败 {file_path}: {str(e)}")
            return None
    
    def should_replace_file(self, file_path):
        """
        判断是否应该替换文件
        
        参数:
            file_path: 目标文件路径
            
        返回:
            (should_replace: bool, reason: str)
        """
        # 检查新DLL文件
        new_dll_path = self.config['new_dll']
        if not os.path.exists(new_dll_path):
            return False, "新DLL文件不存在"
        
        # 强制模式
        if self.config.get('force', False):
            return True, "强制替换模式"
        
        # 比较文件哈希
        old_hash = self.get_file_hash(file_path)
        new_hash = self.get_file_hash(new_dll_path)
        
        if old_hash == new_hash:
            return False, "文件相同，无需替换"
        
        # 比较版本信息
        old_version = self.get_file_version(file_path)
        new_version = self.get_file_version(new_dll_path)
        
        if old_version and new_version:
            if old_version['version'] == new_version['version']:
                return False, f"版本相同 ({old_version['version']})，无需替换"
            else:
                return True, f"版本更新 {old_version['version']} -> {new_version['version']}"
        
        return True, "无法比较版本，执行替换"
    
    def replace_file(self, file_path):
        """
        替换单个文件
        
        参数:
            file_path: 要替换的文件路径
            
        返回:
            True如果成功，False如果失败
        """
        try:
            new_dll_path = self.config['new_dll']
            
            # 检查是否应该替换
            should_replace, reason = self.should_replace_file(file_path)
            if not should_replace:
                self.logger.info(f"跳过文件 {file_path}: {reason}")
                self.operation_stats['files_skipped'] += 1
                return True
            
            self.logger.info(f"准备替换 {file_path}: {reason}")
            
            # 检查文件是否被占用
            if self.is_file_in_use(file_path):
                processes = self.find_processes_using_file(file_path)
                if processes:
                    proc_names = [p['name'] for p in processes]
                    self.logger.warning(f"文件被进程占用 {file_path}: {', '.join(proc_names)}")
                    if not self.config.get('force', False):
                        self.operation_stats['files_skipped'] += 1
                        return False
            
            # 创建备份
            if not self.config.get('no_backup', False):
                backup_path = self.create_backup(file_path)
                if not backup_path:
                    self.logger.error(f"备份失败，跳过替换: {file_path}")
                    self.operation_stats['errors'] += 1
                    return False
            
            # 执行替换
            if not self.config.get('dry_run', False):
                shutil.copy2(new_dll_path, file_path)
                self.logger.info(f"成功替换: {file_path}")
                self.operation_stats['files_replaced'] += 1
            else:
                self.logger.info(f"[预览模式] 将替换: {file_path}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"替换文件失败 {file_path}: {str(e)}")
            self.operation_stats['errors'] += 1
            return False
    
    def save_backup_index(self):
        """保存备份索引文件"""
        try:
            backup_dir = Path(self.config['backup_dir'])
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            index_file = backup_dir / f'backup_index_{timestamp}.json'
            
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(self.backup_index, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"备份索引已保存: {index_file}")
            
        except Exception as e:
            self.logger.error(f"保存备份索引失败: {str(e)}")
    
    def generate_report(self):
        """生成操作报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'config': self.config,
            'statistics': self.operation_stats,
            'backup_index': self.backup_index
        }
        
        try:
            report_dir = Path(self.config.get('report_dir', './reports'))
            report_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = report_dir / f'dll_replacer_report_{timestamp}.json'
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"操作报告已生成: {report_file}")
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {str(e)}")
        
        return report
    
    def run(self):
        """执行主要的替换流程"""
        self.logger.info("=" * 50)
        self.logger.info("DLL文件替换工具开始运行")
        self.logger.info("=" * 50)
        
        # 验证新DLL文件
        new_dll_path = self.config['new_dll']
        if not os.path.exists(new_dll_path):
            self.logger.error(f"新DLL文件不存在: {new_dll_path}")
            return False
        
        self.logger.info(f"新DLL文件: {new_dll_path}")
        new_version = self.get_file_version(new_dll_path)
        if new_version:
            self.logger.info(f"新DLL版本: {new_version['version']}")
        
        # 搜索目标文件
        dll_files = self.search_dll_files()
        
        if not dll_files:
            self.logger.warning("未找到任何目标DLL文件")
            return True
        
        # 显示将要替换的文件
        self.logger.info(f"找到 {len(dll_files)} 个文件需要处理:")
        for file_path in dll_files:
            self.logger.info(f"  - {file_path}")
        
        # 请求用户确认
        if not self.config.get('yes', False) and not self.config.get('dry_run', False):
            response = input(f"\n是否继续替换这 {len(dll_files)} 个文件? (y/N): ")
            if response.lower() not in ['y', 'yes']:
                self.logger.info("用户取消操作")
                return True
        
        # 执行替换
        self.logger.info("开始执行文件替换...")
        for file_path in dll_files:
            self.replace_file(file_path)
        
        # 保存备份索引
        if self.backup_index:
            self.save_backup_index()
        
        # 生成报告
        report = self.generate_report()
        
        # 显示统计信息
        stats = self.operation_stats
        self.logger.info("=" * 50)
        self.logger.info("操作完成统计:")
        self.logger.info(f"  找到文件: {stats['files_found']}")
        self.logger.info(f"  已备份: {stats['files_backed_up']}")
        self.logger.info(f"  已替换: {stats['files_replaced']}")
        self.logger.info(f"  已跳过: {stats['files_skipped']}")
        self.logger.info(f"  错误数: {stats['errors']}")
        self.logger.info("=" * 50)
        
        return stats['errors'] == 0


def get_default_search_paths():
    """获取默认搜索路径"""
    paths = []
    
    # 系统目录
    if os.path.exists(r'E:\VS_Demo\0_Vision\VM'):
        paths.append(r'E:\VS_Demo\0_Vision\VM')
    
    # 程序文件目录
    if os.path.exists(r'E:\VS_Demo\0_Vision\VM'):
        paths.append(r'E:\VS_Demo\0_Vision\VM')
    
    return paths


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='DLL文件替换工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --new-dll "C:/new/halcondotnet.dll" --search-paths "C:/Program Files"
  %(prog)s --new-dll "new.dll" --target-name "halcondotnet.dll" --dry-run
  %(prog)s --new-dll "new.dll" --force --yes
        """
    )
    
    # 必需参数
    parser.add_argument(
        '--new-dll', '-n',
        required=True,
        help='新DLL文件的完整路径'
    )
    
    # 可选参数
    parser.add_argument(
        '--target-name', '-t',
        default='halcondotnet.dll',
        help='目标DLL文件名 (默认: halcondotnet.dll)'
    )
    
    parser.add_argument(
        '--search-paths', '-s',
        nargs='+',
        default=get_default_search_paths(),
        help='搜索路径列表 (默认: 系统和程序目录)'
    )
    
    parser.add_argument(
        '--exclude-paths', '-e',
        nargs='+',
        default=[],
        help='排除的路径列表'
    )
    
    parser.add_argument(
        '--backup-dir', '-b',
        default='./dll_backup',
        help='备份目录 (默认: ./dll_backup)'
    )
    
    parser.add_argument(
        '--dry-run', '-d',
        action='store_true',
        help='预览模式，不实际执行替换'
    )
    
    parser.add_argument(
        '--force', '-f',
        action='store_true',
        help='强制替换，忽略版本检查和文件占用'
    )
    
    parser.add_argument(
        '--yes', '-y',
        action='store_true',
        help='自动确认所有操作'
    )
    
    parser.add_argument(
        '--no-backup',
        action='store_true',
        help='不创建备份文件'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细日志'
    )
    
    parser.add_argument(
        '--log-dir',
        default='./logs',
        help='日志目录 (默认: ./logs)'
    )
    
    parser.add_argument(
        '--report-dir',
        default='./reports',
        help='报告目录 (默认: ./reports)'
    )
    
    args = parser.parse_args()
    
    # 转换为配置字典
    config = {
        'new_dll': args.new_dll,
        'target_name': args.target_name,
        'search_paths': args.search_paths,
        'exclude_paths': args.exclude_paths,
        'backup_dir': args.backup_dir,
        'dry_run': args.dry_run,
        'force': args.force,
        'yes': args.yes,
        'no_backup': args.no_backup,
        'verbose': args.verbose,
        'log_dir': args.log_dir,
        'report_dir': args.report_dir
    }
    
    # 检查管理员权限
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin and not args.dry_run:
            print("警告: 建议以管理员权限运行此脚本以确保能够替换系统文件")
            if not args.yes:
                response = input("是否继续? (y/N): ")
                if response.lower() not in ['y', 'yes']:
                    return 1
    except:
        pass
    
    # 创建并运行替换器
    try:
        replacer = DLLReplacer(config)
        success = replacer.run()
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        return 1
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        return 1


if __name__ == '__main__':
    sys.exit(main()) 