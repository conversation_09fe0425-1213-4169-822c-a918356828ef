#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SeparateUpDownFiles.py - 根据文件名中的关键词分类文件

这个脚本用于将文件名中包含特定关键词（"Up"或"Down"）的文件分类到不同的目录中。
可以选择复制文件（默认）或移动文件到目标目录。

主要功能：
1. 扫描源目录中的所有文件
2. 检测文件名中是否包含"Up"或"Down"关键词
3. 根据关键词将文件复制或移动到对应的目标目录
4. 支持命令行参数自定义源目录和目标目录

适用于需要根据文件名中的特定标识对文件进行自动分类的场景，如分类"上视图"和"下视图"图像。
"""
import os
import shutil
import argparse

def separate_up_down_files(source_dir, up_dir, down_dir):
    """
    将源目录中包含'Up'和'Down'的文件分别移动到相应的目录中
    
    参数:
        source_dir: 源文件目录
        up_dir: 存放包含'Up'的文件的目录
        down_dir: 存放包含'Down'的文件的目录
    """
    # 确保目标目录存在
    os.makedirs(up_dir, exist_ok=True)
    os.makedirs(down_dir, exist_ok=True)
    
    # 计数器
    up_count = 0
    down_count = 0
    other_count = 0
    
    # 处理源目录中的所有文件
    for filename in os.listdir(source_dir):
        file_path = os.path.join(source_dir, filename)
        
        # 只处理文件，不处理目录
        if os.path.isfile(file_path):
            if 'Up' in filename:
                # 移动包含'Up'的文件
                shutil.copy2(file_path, os.path.join(up_dir, filename))
                up_count += 1
                print(f"已复制 'Up' 文件: {filename}")
            elif 'Down' in filename:
                # 移动包含'Down'的文件
                shutil.copy2(file_path, os.path.join(down_dir, filename))
                down_count += 1
                print(f"已复制 'Down' 文件: {filename}")
            else:
                other_count += 1
    
    print(f"\n处理完成!")
    print(f"复制到 '{up_dir}' 的文件数量: {up_count}")
    print(f"复制到 '{down_dir}' 的文件数量: {down_count}")
    print(f"未分类的其他文件数量: {other_count}")

def main():
    parser = argparse.ArgumentParser(description='将包含Up和Down的文件分别移动到不同目录')
    parser.add_argument('--source', '-s', default=r'E:\WH_WorkFiles\ProjectFile\03RPA\DataSets\Side\Disco_DatasetsSideCrop20250315-1\labels\train', help='源文件目录路径 (默认为当前目录)')
    parser.add_argument('--up-dir', '-u', default=r'E:\WH_WorkFiles\ProjectFile\03RPA\DataSets\Side\Disco_DatasetsSideCrop20250315-1\labels\train\Up_Files', help='存放Up文件的目录 (默认为./Up_Files)')
    parser.add_argument('--down-dir', '-d', default=r'E:\WH_WorkFiles\ProjectFile\03RPA\DataSets\Side\Disco_DatasetsSideCrop20250315-1\labels\train\Down_Files', help='存放Down文件的目录 (默认为./Down_Files)')
    parser.add_argument('--move', '-m', action='store_true', help='移动文件而不是复制 (默认为复制)')
    
    args = parser.parse_args()
    
    # 如果用户选择移动而不是复制
    if args.move:
        # 重新定义函数内部的复制操作为移动操作
        global shutil
        shutil.copy2 = shutil.move
        print("注意: 文件将被移动而不是复制")
    
    separate_up_down_files(args.source, args.up_dir, args.down_dir)

if __name__ == "__main__":
    main() 