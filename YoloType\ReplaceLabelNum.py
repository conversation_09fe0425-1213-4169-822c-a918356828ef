#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ReplaceLabelNum.py - 替换YOLO格式标签文件中的类别ID

这个脚本用于批量修改YOLO格式标签文件中的类别ID。例如，可以将标签中
所有类别ID为2的目标替换为类别ID为1，这在数据集类别合并或重组时非常有用。

主要功能：
1. 遍历指定目录中的所有标签文件
2. 读取每个标签文件的内容
3. 将指定的源类别ID替换为目标类别ID
4. 保存修改后的标签文件

适用于需要统一调整数据集类别ID或合并相似类别的场景。
"""
import os
import glob

def update_yolo_labels(directory, source, target):
    # 获取目录中所有txt文件的路径
    txt_files = glob.glob(os.path.join(directory, "*.txt"))

    # 处理每一个txt文件
    for file_path in txt_files:
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                lines = file.readlines()
            
            # 修改标签类别
            new_lines = []
            for line in lines:
                parts = line.strip().split()
                if parts[0] == source:  # 如果标签类别是source
                    parts[0] = target  # 修改为标签类别target
                new_lines.append(' '.join(parts) + '\n')
            
            # 将修改后的内容写入文件
            with open(file_path, 'w', encoding='utf-8', errors='ignore') as file:
                file.writelines(new_lines)
                
            print(f"Updated file: {file_path}")

        except Exception as e:
            print(f"Error processing file {file_path}: {e}")


# 调用函数处理指定目录
directory = r'D:\WH_WorkFiles\ProjectFile\03RPA\DataSet3\Imagewithout2'  # 使用原始字符串避免转义字符问题
source = "2"  # 源类别ID
target = "1"  # 目标类别ID
# 将指定类别替换为指定类别
update_yolo_labels(directory, source, target)
