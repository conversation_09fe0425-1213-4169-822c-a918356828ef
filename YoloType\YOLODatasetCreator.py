#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
YOLODatasetCreator - YOLO格式数据集创建工具

描述:
    此脚本用于创建符合YOLO格式的数据集，将图像和标注文件按比例划分为训练集和验证集。
    生成的数据集结构如下:
    - {output_dir}/images/train: 训练集图像
    - {output_dir}/images/val: 验证集图像
    - {output_dir}/labels/train: 训练集标注
    - {output_dir}/labels/val: 验证集标注

使用方法:
    1. 直接修改脚本底部的参数，然后运行脚本
    2. 从其他Python代码中导入create_yolo_dataset函数并调用

参数说明:
    - images_dir: 原始图像文件目录路径
    - labels_dir: 原始标注文件目录路径 (标注文件应与图像文件同名，后缀不同)
    - output_dir: 输出数据集的根目录路径
    - val_ratio: 验证集占比，默认0.2 (即20%验证集，80%训练集)
    - img_ext: 图像文件扩展名，默认['.jpg', '.jpeg', '.png', '.bmp']
    - label_ext: 标注文件扩展名，默认'.txt'
    - seed: 随机种子，用于确保数据集划分可重现，默认42
"""

import os
import shutil
import random
import glob
from tqdm import tqdm

def create_directories(output_dir):
    """创建YOLO格式数据集目录结构"""
    dirs = [
        os.path.join(output_dir, 'images', 'train'),
        os.path.join(output_dir, 'images', 'val'),
        os.path.join(output_dir, 'labels', 'train'),
        os.path.join(output_dir, 'labels', 'val')
    ]
    
    for d in dirs:
        os.makedirs(d, exist_ok=True)
        print(f"创建目录: {d}")
    
    return dirs

def collect_files(image_dir, label_dir, img_exts, label_ext):
    """收集图像和标注文件"""
    # 获取所有图像文件
    image_files = []
    
    # 支持多种图像扩展名
    if isinstance(img_exts, str):
        img_exts = [img_exts]  # 转换单个字符串为列表
    
    for ext in img_exts:
        image_files.extend(glob.glob(os.path.join(image_dir, f"*{ext}")))
    
    image_names = [os.path.basename(f) for f in image_files]
    
    # 准备标注文件路径
    image_base_names = [os.path.splitext(name)[0] for name in image_names]
    label_files = [os.path.join(label_dir, f"{base}{label_ext}") for base in image_base_names]
    
    # 筛选出同时有图像和标注的文件
    valid_pairs = []
    for img_file, label_file in zip(image_files, label_files):
        if os.path.exists(label_file):
            valid_pairs.append((img_file, label_file))
        else:
            print(f"警告: 找不到标注文件 {label_file}")
    
    return valid_pairs

def split_dataset(file_pairs, val_ratio, seed):
    """划分训练集和验证集"""
    random.seed(seed)
    random.shuffle(file_pairs)
    
    val_count = int(len(file_pairs) * val_ratio)
    train_pairs = file_pairs[val_count:]
    val_pairs = file_pairs[:val_count]
    
    return train_pairs, val_pairs

def copy_files(file_pairs, target_img_dir, target_label_dir):
    """复制文件到目标目录"""
    for img_file, label_file in tqdm(file_pairs, desc="复制文件"):
        # 复制图像
        img_filename = os.path.basename(img_file)
        shutil.copy2(img_file, os.path.join(target_img_dir, img_filename))
        
        # 复制标注
        label_filename = os.path.basename(label_file)
        shutil.copy2(label_file, os.path.join(target_label_dir, label_filename))

def create_yolo_dataset(images_dir, labels_dir, output_dir, val_ratio=0.2, 
                        img_ext=['.jpg', '.jpeg', '.png', '.bmp'], label_ext='.txt', seed=42):
    """
    创建YOLO格式数据集并划分训练集和验证集
    
    参数:
        images_dir (str): 原始图像文件目录路径
        labels_dir (str): 原始标注文件目录路径
        output_dir (str): 输出数据集根目录路径
        val_ratio (float): 验证集比例，默认为0.2
        img_ext (list/str): 图像文件扩展名，可以是字符串或列表，默认为['.jpg', '.jpeg', '.png', '.bmp']
        label_ext (str): 标注文件扩展名，默认为.txt
        seed (int): 随机种子，默认为42
    """
    # 创建目录结构
    train_img_dir, val_img_dir, train_label_dir, val_label_dir = create_directories(output_dir)
    
    # 收集文件
    print("正在收集文件...")
    file_pairs = collect_files(images_dir, labels_dir, img_ext, label_ext)
    print(f"找到 {len(file_pairs)} 对有效的图像和标注文件")
    
    if not file_pairs:
        print("错误: 未找到有效的图像和标注文件对")
        return
    
    # 划分数据集
    print(f"使用验证集比例 {val_ratio} 划分数据集...")
    train_pairs, val_pairs = split_dataset(file_pairs, val_ratio, seed)
    print(f"划分结果: {len(train_pairs)} 训练样本, {len(val_pairs)} 验证样本")
    
    # 复制文件
    print("正在复制训练集文件...")
    copy_files(train_pairs, train_img_dir, train_label_dir)
    
    print("正在复制验证集文件...")
    copy_files(val_pairs, val_img_dir, val_label_dir)
    
    print(f"\n数据集创建完成! 输出目录: {output_dir}")
    print(f"训练集: {len(train_pairs)} 样本")
    print(f"验证集: {len(val_pairs)} 样本")
    print("\n用法示例:")
    print("- 训练: 设置 train 数据路径为 {}/images/train 和 {}/labels/train".format(output_dir, output_dir))
    print("- 验证: 设置 val 数据路径为 {}/images/val 和 {}/labels/val".format(output_dir, output_dir))

if __name__ == "__main__":
    # 直接在代码中传入参数示例
    create_yolo_dataset(
        images_dir=r"C:\Users\<USER>\Desktop\kerfcropout",  # 原始图像目录
        labels_dir=r"C:\Users\<USER>\Desktop\labels",  # 原始标注目录
        output_dir=r"C:\Users\<USER>\Desktop\datasets",     # 输出目录
        val_ratio=0.2,                           # 验证集比例
        img_ext=['.jpg', '.jpeg', '.png', '.bmp'], # 支持多种图像扩展名
        label_ext='.txt',                        # 标注文件扩展名
        seed=42                                  # 随机种子
    ) 