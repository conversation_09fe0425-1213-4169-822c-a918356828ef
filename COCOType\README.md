# COCO数据集可视化和验证工具

本工具集提供了COCO格式数据集的可视化、验证和分析功能，帮助您快速查看和验证标注信息。

## 📁 文件说明

### 🎨 可视化工具
- **`simple_coco_viewer.py`**: 简化版可视化工具（**推荐新手使用**）
- `coco_visualizer.py`: 完整版可视化工具（功能丰富，支持命令行）

### 🔍 验证工具
- **`coco_validator.py`**: COCO数据集验证工具（**推荐使用**）

## 🚀 快速开始

### 方法1：简单可视化（推荐）

使用 `simple_coco_viewer.py` 快速查看COCO数据集：

1. **修改配置参数**
   ```python
   # 编辑 simple_coco_viewer.py 中的配置
   ANNOTATION_FILE = r"E:\datasets\coco_dataset\annotations\train.json"
   IMAGES_DIR = r"E:\datasets\coco_dataset\images\train"
   ```

2. **运行脚本**
   ```bash
   python simple_coco_viewer.py
   ```

3. **功能特性**
   - 📊 显示数据集统计信息
   - 🎲 随机可视化图片样本
   - 🏷️ 按类别查看样本
   - 🔍 检查标注质量

### 方法2：数据集验证（推荐）

使用 `coco_validator.py` 验证COCO数据集的完整性：

1. **修改配置参数**
   ```python
   # 编辑 coco_validator.py 中的配置
   ANNOTATION_FILE = r"E:\datasets\coco_dataset\annotations\train.json"
   IMAGES_DIR = r"E:\datasets\coco_dataset\images\train"
   ```

2. **运行验证**
   ```bash
   python coco_validator.py
   ```

3. **验证内容**
   - ✅ 文件存在性检查
   - ✅ JSON格式验证
   - ✅ COCO结构验证
   - ✅ 图片文件验证
   - ✅ 标注数据验证
   - ✅ 数据一致性检查

### 方法3：完整可视化工具

使用 `coco_visualizer.py` 进行高级可视化：

**命令行使用：**
```bash
# 显示统计信息
python coco_visualizer.py --annotation annotations.json --images images/ --stats

# 随机可视化5张图片
python coco_visualizer.py --annotation annotations.json --images images/ --random 5

# 可视化指定类别的样本
python coco_visualizer.py --annotation annotations.json --images images/ --category person

# 生成类别分布图表
python coco_visualizer.py --annotation annotations.json --images images/ --chart

# 导出标注摘要
python coco_visualizer.py --annotation annotations.json --images images/ --export summary.json
```

## 🔧 依赖库

```bash
pip install opencv-python matplotlib pillow numpy
```

## 📊 功能详解

### 1. 数据集统计信息

显示以下统计信息：
- 📁 图片数量、标注数量、类别数量
- 📈 每个类别的标注分布
- 📊 图片标注数量统计（最少、最多、平均）

### 2. 可视化功能

- **边界框可视化**: 显示检测框和类别标签
- **分割掩码可视化**: 显示分割多边形
- **随机样本**: 随机选择图片进行可视化
- **类别样本**: 查看特定类别的标注样本
- **自定义样式**: 可调整颜色、线条粗细等

### 3. 验证功能

- **文件完整性**: 检查图片文件是否存在
- **JSON格式**: 验证标注文件格式正确性
- **数据一致性**: 检查ID唯一性、引用完整性
- **标注质量**: 验证边界框、分割数据的有效性
- **图片尺寸**: 验证记录的尺寸与实际尺寸是否匹配

## 📈 使用示例

### 示例1：快速查看数据集

```python
# 使用 simple_coco_viewer.py
from simple_coco_viewer import *

# 加载数据
coco_data = load_coco_data("annotations.json")

# 显示信息
show_dataset_info(coco_data)

# 随机可视化
visualize_random_samples(coco_data, "images/", num_samples=3)
```

### 示例2：验证数据集

```python
# 使用 coco_validator.py
from coco_validator import COCOValidator

# 创建验证器
validator = COCOValidator("annotations.json", "images/")

# 执行验证
is_valid = validator.validate_all()

# 获取报告
report = validator.get_validation_report()
```

### 示例3：高级可视化

```python
# 使用 coco_visualizer.py
from coco_visualizer import COCOVisualizer

# 创建可视化器
visualizer = COCOVisualizer("annotations.json", "images/")

# 显示统计
visualizer.print_statistics()

# 可视化特定图片
visualizer.visualize_image(image_id=0)

# 可视化类别样本
visualizer.visualize_category_samples("person", num_samples=4)

# 生成分布图表
visualizer.create_category_distribution_chart("distribution.png")
```

## ⚠️ 注意事项

1. **路径配置**: 确保标注文件和图片目录路径正确
2. **文件格式**: 支持常见图片格式（jpg, png, bmp等）
3. **内存使用**: 大数据集可能需要较多内存
4. **显示设置**: 确保系统支持matplotlib图形显示

## 🔍 常见问题

### Q1: 图片无法显示怎么办？
**A:** 检查图片路径是否正确，确保图片文件存在且格式支持。

### Q2: 验证失败怎么办？
**A:** 查看详细的错误信息，通常是文件路径、JSON格式或标注数据有问题。

### Q3: 如何查看特定类别的样本？
**A:** 使用 `visualize_category_samples()` 函数，指定类别名称。

### Q4: 如何保存可视化结果？
**A:** 在函数中指定 `save_path` 参数，或使用命令行的 `--output` 参数。

### Q5: 支持哪些COCO格式？
**A:** 支持标准COCO格式，包括检测和分割任务。

## 📊 输出示例

### 验证结果示例
```
🔍 COCO数据集验证工具
============================================================
📁 检查文件存在性...
📄 检查JSON格式...
🏗️  检查COCO数据结构...
🖼️  检查图片文件...
🏷️  检查标注数据...
🔗 检查数据一致性...

📋 验证结果汇总
============================================================
📊 数据集统计:
   图片数量: 1000
   标注数量: 2500
   类别数量: 5

🏷️  类别列表:
    0: person
    1: car
    2: bicycle
    3: motorcycle
    4: bus

🔍 验证结果:
   ✅ 信息: 6
   ⚠️  警告: 2
   ❌ 错误: 0

🎉 验证通过！数据集格式正确
============================================================
```

### 统计信息示例
```
📊 COCO数据集信息
============================================================
📁 基本统计:
   图片数量: 1000
   标注数量: 2500
   类别数量: 5

🏷️  类别列表:
    1. person (ID: 0)
    2. car (ID: 1)
    3. bicycle (ID: 2)
    4. motorcycle (ID: 3)
    5. bus (ID: 4)

📈 类别分布:
         person:  1200 个标注 ( 48.0%)
            car:   800 个标注 ( 32.0%)
        bicycle:   300 个标注 ( 12.0%)
     motorcycle:   150 个标注 (  6.0%)
            bus:    50 个标注 (  2.0%)
============================================================
```

## 🎉 完成

现在您可以使用这些工具来：
- ✅ 快速查看COCO数据集的内容
- ✅ 验证数据集的完整性和正确性
- ✅ 可视化标注信息
- ✅ 分析数据集的分布情况
- ✅ 导出统计报告

这些工具特别适合在训练模型前验证数据集质量，或者在数据集转换后检查结果的正确性。