"""
YOLO到COCO格式转换示例
修改下面的配置参数后直接运行即可
"""

from yolo2coco_config import YoloToCoco, yolo_to_coco_detection, yolo_to_coco_segmentation
import os


def main():
    # ==================== 配置参数 ====================
    # 请根据您的实际情况修改以下路径
    
    IMAGES_DIR = r"C:\Users\<USER>\Desktop\datasets\images"              # 图片文件夹路径
    LABELS_DIR = r"C:\Users\<USER>\Desktop\datasets\labels"              # YOLO标注文件夹路径
    OUTPUT_PATH = r"E:\datasets\annotations.json"   # 输出COCO文件路径
    
    # 类别名称列表（按照YOLO标注文件中的类别ID顺序）
    CLASS_NAMES = [
        "person",       # 类别ID: 0
        "car",          # 类别ID: 1
        "bicycle",      # 类别ID: 2
        # 添加更多类别...
    ]
    
    # 任务类型选择
    TASK_TYPE = "detection"  # "detection" 或 "segmentation"
    
    # ==================== 执行转换 ====================
    
    print("=" * 50)
    print("YOLO到COCO格式转换工具")
    print("=" * 50)
    
    # 检查路径是否存在
    if not os.path.exists(IMAGES_DIR):
        print(f"错误：图片文件夹不存在: {IMAGES_DIR}")
        return
    
    if not os.path.exists(LABELS_DIR):
        print(f"错误：标注文件夹不存在: {LABELS_DIR}")
        return
    
    # 创建输出目录
    output_dir = os.path.dirname(OUTPUT_PATH)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    
    print(f"图片文件夹: {IMAGES_DIR}")
    print(f"标注文件夹: {LABELS_DIR}")
    print(f"输出文件: {OUTPUT_PATH}")
    print(f"类别数量: {len(CLASS_NAMES)}")
    print(f"类别列表: {CLASS_NAMES}")
    print(f"任务类型: {TASK_TYPE}")
    print("-" * 50)
    
    try:
        # 方法1: 使用类（推荐）
        converter = YoloToCoco(IMAGES_DIR, LABELS_DIR, CLASS_NAMES, OUTPUT_PATH)
        
        if TASK_TYPE == "detection":
            coco_data = converter.convert_detection()
        elif TASK_TYPE == "segmentation":
            coco_data = converter.convert_segmentation()
        else:
            print(f"错误：不支持的任务类型 {TASK_TYPE}")
            return
        
        # 方法2: 使用便捷函数（可选）
        # if TASK_TYPE == "detection":
        #     coco_data = yolo_to_coco_detection(IMAGES_DIR, LABELS_DIR, CLASS_NAMES, OUTPUT_PATH)
        # elif TASK_TYPE == "segmentation":
        #     coco_data = yolo_to_coco_segmentation(IMAGES_DIR, LABELS_DIR, CLASS_NAMES, OUTPUT_PATH)
        
        print("=" * 50)
        print("转换成功完成！")
        print(f"输出文件大小: {os.path.getsize(OUTPUT_PATH) / 1024:.2f} KB")
        
    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()


def quick_detection_example():
    """快速检测任务转换示例"""
    # 快速配置示例
    images_dir = r"E:\my_dataset\images"
    labels_dir = r"E:\my_dataset\labels"
    class_names = ["person", "car"]
    output_path = r"E:\my_dataset\coco_annotations.json"
    
    try:
        coco_data = yolo_to_coco_detection(images_dir, labels_dir, class_names, output_path)
        print("快速转换完成！")
    except Exception as e:
        print(f"快速转换失败: {e}")


def quick_segmentation_example():
    """快速分割任务转换示例"""
    # 快速配置示例
    images_dir = r"E:\my_dataset\images"
    labels_dir = r"E:\my_dataset\labels"
    class_names = ["person", "car"]
    output_path = r"E:\my_dataset\coco_seg_annotations.json"
    
    try:
        coco_data = yolo_to_coco_segmentation(images_dir, labels_dir, class_names, output_path)
        print("快速分割转换完成！")
    except Exception as e:
        print(f"快速分割转换失败: {e}")


if __name__ == "__main__":
    # 运行主转换程序
    main()
    
    # 或者运行快速示例
    # quick_detection_example()
    # quick_segmentation_example()