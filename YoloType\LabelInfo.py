#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
LabelInfo.py - 读取和检查YOLO格式标签文件的内容

这个脚本提供了两个主要功能：
1. 读取并打印指定目录下所有txt标签文件的内容
2. 查找并打印包含特定类别ID（如类别2）的标签文件

这对于检查和验证YOLO数据集中的标签文件非常有用，尤其是在进行数据集审核、查找特定类别标签时。
"""
import os
import glob

def read_and_print_txt_files(directory):
    # 获取目录下所有txt文件的路径
    txt_files = glob.glob(os.path.join(directory, "*.txt"))

    # 处理每一个txt文件
    for file_path in txt_files:
        try:
            # 打开并读取文件内容，尝试使用UTF-8编码
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                # 打印文件名
                print(f"\nReading file: {file_path}\n{'-'*40}")
                # 逐行读取并打印文件内容
                for line in file:
                    print(line.strip())  # 使用strip()去除每行末尾的换行符
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")


def print_files_with_label_2(directory):
    """搜索目录中的所有txt文件，打印包含标签类别为2的文件及其内容"""
    txt_files = glob.glob(os.path.join(directory, "*.txt"))

    for file_path in txt_files:
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                lines = file.readlines()
            
            contains_label_2 = False
            for line in lines:
                parts = line.strip().split()
                if parts[0] == '2':  # 如果标签类别是2
                    contains_label_2 = True
                    break

            if contains_label_2:
                print(f"\nFile containing label 2: {file_path}\n{'-'*40}")
                for line in lines:
                    parts = line.strip().split()
                    if parts[0] == '2':
                        print(line.strip())

        except Exception as e:
            print(f"Error processing file {file_path}: {e}")

# 调用函数处理指定目录
directory = r"D:\WH_WorkFiles\ProjectFile\03RPA\24240708\lmage1"  # 使用原始字符串避免转义字符问题
# read_and_print_txt_files(directory)
# 打印每个标签的信息
print_files_with_label_2(directory)



 