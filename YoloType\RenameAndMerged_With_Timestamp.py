#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
RenameAndMerged_With_Timestamp.py - 合并多个文件夹中的文件并统一命名

这个脚本用于将多个子目录中的文件合并到一个目标目录，并使用统一的时间戳和序号进行重命名。
合并后的文件名格式为"YYYYMMDD_HHMM_序号.扩展名"，使得文件按时间和顺序排列。

主要功能：
1. 扫描父目录下的所有子目录
2. 收集所有子目录中的文件
3. 为所有文件分配相同的时间戳和递增的序号
4. 将文件复制到指定的目标目录，并按新命名规则重命名

适用于需要将分散在多个目录的文件整合到一处并统一命名的场景，如数据集合并或文件归档。
"""
import os
from datetime import datetime
import sys
import shutil

def get_subdirectories(parent_dir):
    """
    获取指定目录下的所有子文件夹路径
    """
    subdirs = []
    for item in os.listdir(parent_dir):
        item_path = os.path.join(parent_dir, item)
        if os.path.isdir(item_path):
            subdirs.append(item_path)
    return subdirs

def merge_and_rename_files(source_dirs, output_dir):
    """
    合并多个文件夹中的文件并按统一格式重命名
    source_dirs: 源文件夹路径列表
    output_dir: 输出目录路径
    """
    # 检查源目录是否都存在
    for dir_path in source_dirs:
        if not os.path.exists(dir_path):
            print(f"错误：目录不存在 - {dir_path}")
            return False

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 获取当前时间戳（所有文件都使用这个时间戳）
    timestamp = datetime.now().strftime('%Y%m%d_%H%M')
    
    # 收集所有文件
    all_files = []
    for dir_path in source_dirs:
        print(f"正在处理文件夹: {dir_path}")
        for filename in os.listdir(dir_path):
            file_path = os.path.join(dir_path, filename)
            if os.path.isfile(file_path):
                all_files.append((file_path, filename))
    
    if not all_files:
        print("警告：没有找到任何文件")
        return False
        
    print(f"\n共找到 {len(all_files)} 个文件，开始处理...")
    
    # 重命名并复制文件
    for index, (src_path, old_filename) in enumerate(all_files, start=1):
        # 获取文件扩展名
        _, ext = os.path.splitext(old_filename)
        # 新文件名格式：时间戳_序号.扩展名
        new_filename = f"{timestamp}_{index:04d}{ext}"
        dst_path = os.path.join(output_dir, new_filename)
        
        try:
            shutil.copy2(src_path, dst_path)
            print(f"已复制并重命名: {old_filename} -> {new_filename}")
        except Exception as e:
            print(f"处理文件 {old_filename} 时发生错误: {str(e)}")
    
    print(f"\n完成！共处理 {len(all_files)} 个文件")
    print(f"所有文件已合并到: {output_dir}")
    return True

if __name__ == "__main__":
    # 设置包含所有数据集文件夹的父目录路径
    parent_directory = r"C:\Users\<USER>\Desktop\1\01"  # 在这里修改为您的父目录路径
    
    # 设置输出目录路径
    output_directory = r"D:\merged_dataset"  # 在这里修改为您的输出目录路径
    
    # 自动获取所有子文件夹路径
    source_directories = get_subdirectories(parent_directory)
    
    if not source_directories:
        print(f"错误：在 {parent_directory} 中没有找到任何子文件夹")
        sys.exit(1)
        
    print(f"找到以下子文件夹：")
    for dir_path in source_directories:
        print(f"- {dir_path}")
    
    # 执行合并和重命名操作
    merge_and_rename_files(source_directories, output_directory) 