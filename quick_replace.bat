@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ================================================
echo           DLL File Replacer Quick Start
echo ================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python not found, please install Python first
    pause
    exit /b 1
)

REM Check if dll_replacer.py exists
if not exist "dll_replacer.py" (
    echo Error: dll_replacer.py file not found
    pause
    exit /b 1
)

REM Prompt user for new DLL path
set /p NEW_DLL="Enter the full path of new DLL file: "
if "%NEW_DLL%"=="" (
    echo Error: New DLL path cannot be empty
    pause
    exit /b 1
)

REM Check if new DLL file exists
if not exist "%NEW_DLL%" (
    echo Error: Specified new DLL file does not exist: %NEW_DLL%
    pause
    exit /b 1
)

echo.
echo New DLL file: %NEW_DLL%
echo.

REM Ask for operation mode
echo Please select operation mode:
echo 1. Preview mode (Recommended, only view files to be replaced)
echo 2. Actual replacement mode
echo.
set /p MODE="Enter your choice (1 or 2): "

if "%MODE%"=="1" (
    echo.
    echo Starting preview mode...
    python dll_replacer.py --new-dll "%NEW_DLL%" --dry-run --verbose
) else (
    if "%MODE%"=="2" (
        echo.
        echo Warning: About to perform actual replacement operation!
        set /p CONFIRM="Are you sure to continue? (y/N): "
        if /i "%CONFIRM%"=="y" (
            echo.
            echo Performing DLL replacement...
            python dll_replacer.py --new-dll "%NEW_DLL%" --verbose
        ) else (
            echo Operation cancelled
        )
    ) else (
        echo Invalid choice
    )
)

echo.
echo Operation completed, press any key to exit...
pause >nul 