#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除没有对应YOLO标签文件的图像
Delete images without corresponding YOLO label files

专门用于YOLO格式数据集清理
"""

import os
import glob
from pathlib import Path

# ==================== 配置区域 ====================

# 数据集目录 (图像和标签在同一目录)
DATASET_DIRECTORY = "."

# 或者分别指定图像和标签目录
IMAGE_DIRECTORY = "C:/Users/<USER>/Desktop/kerfcropout"
LABEL_DIRECTORY = "C:/Users/<USER>/Desktop/labels"

# 是否使用分离的目录 (True=使用上面的分离目录, False=使用同一目录)
USE_SEPARATE_DIRECTORIES = True

# 是否递归搜索子目录
RECURSIVE_SEARCH = True

# 是否只预览不删除
PREVIEW_ONLY = False

# 是否需要确认才删除
REQUIRE_CONFIRMATION = True

# ==================== 配置区域结束 ====================


def get_image_label_pairs(img_dir, label_dir, recursive=True):
    """
    获取图像和标签文件的配对信息
    
    Returns:
        tuple: (图像文件字典, 标签文件字典, 没有标签的图像列表)
    """
    # 支持的图像格式
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif']
    
    # 获取所有图像文件
    image_files = {}
    for ext in image_extensions:
        if recursive:
            pattern = os.path.join(img_dir, "**", ext)
            files = glob.glob(pattern, recursive=True)
        else:
            pattern = os.path.join(img_dir, ext)
            files = glob.glob(pattern)
        
        for img_path in files:
            # 获取不含扩展名的文件名
            img_name = Path(img_path).stem
            image_files[img_name] = img_path
    
    # 获取所有标签文件 (.txt)
    label_files = {}
    if recursive:
        pattern = os.path.join(label_dir, "**", "*.txt")
        txt_files = glob.glob(pattern, recursive=True)
    else:
        pattern = os.path.join(label_dir, "*.txt")
        txt_files = glob.glob(pattern)
    
    for label_path in txt_files:
        label_name = Path(label_path).stem
        label_files[label_name] = label_path
    
    # 找出没有对应标签的图像
    images_without_labels = []
    for img_name, img_path in image_files.items():
        if img_name not in label_files:
            images_without_labels.append(img_path)
    
    return image_files, label_files, images_without_labels


def main():
    """主函数"""
    print("=" * 70)
    print("删除没有对应YOLO标签文件的图像")
    print("=" * 70)
    
    # 确定图像和标签目录
    if USE_SEPARATE_DIRECTORIES:
        img_dir = IMAGE_DIRECTORY
        label_dir = LABEL_DIRECTORY
        print(f"图像目录: {os.path.abspath(img_dir)}")
        print(f"标签目录: {os.path.abspath(label_dir)}")
    else:
        img_dir = label_dir = DATASET_DIRECTORY
        print(f"数据集目录: {os.path.abspath(DATASET_DIRECTORY)}")
    
    print(f"递归搜索: {'是' if RECURSIVE_SEARCH else '否'}")
    print(f"预览模式: {'是' if PREVIEW_ONLY else '否'}")
    print("-" * 70)
    
    # 检查目录是否存在
    if not os.path.exists(img_dir):
        print(f"错误: 图像目录 '{img_dir}' 不存在")
        return
    
    if not os.path.exists(label_dir):
        print(f"错误: 标签目录 '{label_dir}' 不存在")
        return
    
    # 获取文件配对信息
    print("正在分析图像和标签文件...")
    image_files, label_files, images_without_labels = get_image_label_pairs(
        img_dir, label_dir, RECURSIVE_SEARCH
    )
    
    # 显示统计信息
    print(f"找到图像文件: {len(image_files)} 个")
    print(f"找到标签文件: {len(label_files)} 个")
    print(f"有标签的图像: {len(image_files) - len(images_without_labels)} 个")
    print(f"无标签的图像: {len(images_without_labels)} 个")
    
    if len(image_files) > 0:
        completion_rate = (len(image_files) - len(images_without_labels)) / len(image_files) * 100
        print(f"标注完成率: {completion_rate:.1f}%")
    
    if not images_without_labels:
        print("\n所有图像都有对应的标签文件，无需删除")
        return
    
    print(f"\n发现 {len(images_without_labels)} 个没有标签的图像:")
    
    # 显示无标签的图像列表
    for i, img_path in enumerate(images_without_labels[:10], 1):  # 只显示前10个
        print(f"  {i:2d}. {img_path}")
    
    if len(images_without_labels) > 10:
        print(f"     ... 还有 {len(images_without_labels) - 10} 个文件")
    
    # 预览模式
    if PREVIEW_ONLY:
        print(f"\n=== 预览模式 ===")
        print(f"将删除 {len(images_without_labels)} 个没有标签的图像文件")
        return
    
    # 确认删除
    if REQUIRE_CONFIRMATION:
        print(f"\n准备删除 {len(images_without_labels)} 个没有标签的图像文件")
        confirm = input("确认删除吗? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("操作已取消")
            return
    
    # 执行删除
    print("\n开始删除文件...")
    deleted_count = 0
    failed_count = 0
    
    for img_path in images_without_labels:
        try:
            os.remove(img_path)
            print(f"已删除: {os.path.basename(img_path)}")
            deleted_count += 1
        except OSError as e:
            print(f"删除失败 {img_path}: {e}")
            failed_count += 1
    
    print(f"\n删除完成！")
    print(f"成功删除: {deleted_count} 个图像文件")
    if failed_count > 0:
        print(f"删除失败: {failed_count} 个图像文件")
    
    # 显示最终统计
    remaining_images = len(image_files) - deleted_count
    if remaining_images > 0:
        final_completion_rate = (remaining_images - (len(images_without_labels) - deleted_count)) / remaining_images * 100
        print(f"清理后标注完成率: {final_completion_rate:.1f}%")


if __name__ == "__main__":
    main()