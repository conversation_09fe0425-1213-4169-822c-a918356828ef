#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Drawlabel.py - 绘制YOLO格式标注的边界框并保存可视化结果

这个脚本用于将YOLO格式标注的边界框绘制到对应的图像上，并保存可视化结果。
它有两个主要功能：
1. 为有标注的图像绘制边界框并保存到输出目录
2. 将没有对应标注文件的图像复制到未标注目录

可以处理多种图像格式（PNG, JPG, JPEG, BMP, TIF, TIFF），并支持放大图像以便更清晰地查看标注。
这对于标注数据的可视化检查和验证非常有用。
"""
import cv2
import os
import glob
import shutil

def draw_yolo_bboxes(image_directory, label_directory, output_directory, unlabeled_directory):
    # 读取图像目录下所有png图像文件的路径
    
    image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tif', '*.tiff']
    image_paths = []
    for ext in image_extensions:
        image_paths.extend(glob.glob(os.path.join(image_directory, ext)))
    
    for image_path in image_paths:
        # 根据图像路径生成对应的标签文件路径
        label_path = os.path.splitext(os.path.basename(image_path))[0] + ".txt"
        label_path = os.path.join(label_directory, label_path)
        
        if not os.path.exists(label_path):
            print(f"Label file for {image_path} does not exist.")
            # 如果没有标签文件的图像，复制到未标注目录
            shutil.copy2(image_path, os.path.join(unlabeled_directory, os.path.basename(image_path)))
            continue

        # 读取图像
        image = cv2.imread(image_path)
        height, width, _ = image.shape
        # 放大图像
        scale = 2
        image_large = cv2.resize(image, (width * scale, height * scale))
        
        # 读取标签文件
        with open(label_path, 'r') as file:
            labels = file.readlines()

        for label in labels:
            parts = label.strip().split()
            class_id = int(parts[0])
            x_center = float(parts[1]) * width
            y_center = float(parts[2]) * height
            bbox_width = float(parts[3]) * width
            bbox_height = float(parts[4]) * height

            # 坐标也要相应放大
            x_min = int(x_center * scale - bbox_width * scale / 2)
            y_min = int(y_center * scale - bbox_height * scale / 2)
            x_max = int(x_center * scale + bbox_width * scale / 2)
            y_max = int(y_center * scale + bbox_height * scale / 2)

            # 在大图上绘制
            cv2.rectangle(image_large, (x_min, y_min), (x_max, y_max), (0, 255, 0), 2)
            cv2.putText(image_large, str(class_id), (x_min, y_min - 20), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        # 缩小回原始尺寸
        image = cv2.resize(image_large, (width, height))

        # 构建输出文件路径
        output_path = os.path.join(output_directory, os.path.basename(image_path))
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 保存图像
        cv2.imwrite(output_path, image)
        print(f"save image {output_path}")

    cv2.destroyAllWindows()

if __name__=="__main__":

    # 使用命令行参数指定要处理的目录
    # datapath=r"D:\WH_WorkFiles\ProjectFile\03RPA\DataSets\NewDataSet"
    # datapath=r"D:\WH_WorkFiles\ProjectFile\03RPA\DataSets\DataSet"
    datapath=r"C:\Users\<USER>\Desktop\DatasetsKerfCrop20252027Demo"
    image_directory = rf"{datapath}\images\train" # 图像目录路径
    label_directory = rf"{datapath}\labels\train"  # 标签文件目录路径
    output_directory = rf"{datapath}\output\train"  # 输出目录路径?
    unlabeled_directory = rf"{datapath}\output\unlabeled"

    # 创建未标注图像和文件的目录
    os.makedirs(unlabeled_directory, exist_ok=True)

    # 绘制每张标注图像和未标注图像
    draw_yolo_bboxes(image_directory, label_directory, output_directory, unlabeled_directory)