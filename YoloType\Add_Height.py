#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Add_Height.py - 调整YOLO格式标注中边界框的高度

这个脚本用于增加或减少YOLO格式标注文件中边界框的高度。
可以根据不同的类别ID，对边界框应用不同的高度调整量。
同时，脚本将生成可视化结果，显示调整后的边界框。

它支持两种模式：
1. 增加边界框高度 (addheight=True)
2. 减少边界框高度 (addheight=False)
"""
import cv2
import os
import glob

def draw_yolo_bboxes(image_directory, label_directory, output_directory,addheight, isshow=True, height1_increase=10, height2_increase=10):
    # 获取图片目录下所有png图片文件的路径
    image_paths = glob.glob(os.path.join(image_directory, "*.jpeg"))

    for image_path in image_paths:
        # 根据图片路径构造对应标签文件路径
        label_path = os.path.splitext(os.path.basename(image_path))[0] + ".txt"
        label_path = os.path.join(label_directory, label_path)
        
        if not os.path.exists(label_path):
            print(f"Label file for {image_path} does not exist.")
            continue

        # 获取图片
        image = cv2.imread(image_path)
        height, width, _ = image.shape

        # 获取标签文件
        with open(label_path, 'r') as file:
            labels = file.readlines()

        yolo_labels = []
        for label in labels:
            parts = label.strip().split()
            class_id = int(parts[0])
            x_center = float(parts[1]) * width
            y_center = float(parts[2]) * height
            bbox_width = float(parts[3]) * width
            bbox_height = float(parts[4]) * height
            # 计算边框坐标
            x_min = int(x_center - bbox_width / 2)
            y_min = int(y_center - bbox_height / 2)
            x_max = int(x_center + bbox_width / 2)
            y_max = int(y_center + bbox_height / 2)
            if class_id == 0:
                if addheight:
                    # 增加高度
                    y_min = max(0, y_min - height1_increase)
                    y_max = min(height, y_max + height1_increase)
                else:
                    # 减少高度
                    y_min = max(0, y_min + height1_increase)
                    y_max = min(height, y_max - height1_increase)
            # 对类别ID为1的框增加边框高度
            if class_id == 1:
                if addheight:
                    # 增加高度
                    y_min = max(0, y_min - height2_increase)
                    y_max = min(height, y_max + height2_increase)
                else:
                    # 减少高度
                    y_min = max(0, y_min + height2_increase)
                    y_max = min(height, y_max - height2_increase)
            # 计算新的边框高度
            new_y_center = (y_min + y_max) / 2
            new_bbox_height = y_max - y_min
            # 绘制边框
            cv2.rectangle(image, (x_min, y_min), (x_max, y_max), (0, 255, 0), 2)
            # 在边框上写上类别ID
            cv2.putText(image, str(class_id), (x_min, y_min - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            # 修改边框信息并转为YOLO格式
            yolo_x = x_center / width  # x中心点坐标
            yolo_y = new_y_center / height  # 使用新的y中心点坐标
            yolo_w = bbox_width / width  # 宽度比例
            yolo_h = new_bbox_height / height  # 使用新的高度
            yolo_labels.append(f"{class_id} {yolo_x:.6f} {yolo_y:.6f} {yolo_w:.6f} {yolo_h:.6f}\n")

        # 构造输出文件路径
        output_image_path = os.path.join(output_directory, os.path.basename(image_path))
        output_label_path = os.path.join(output_directory, os.path.splitext(os.path.basename(image_path))[0] + ".txt")
        os.makedirs(output_directory, exist_ok=True)
        
        # 保存图片
        cv2.imwrite(output_image_path, image)

        # 保存YOLO格式标签文件
        with open(output_label_path, 'w') as file:
            file.writelines(yolo_labels )

# 指定要处理的目录
# image_directory = r"D:\WH_WorkFiles\ProjectFile\03RPA\DataSet2\images\train"  # 图片目录路径
# label_directory = r"D:\WH_WorkFiles\ProjectFile\03RPA\DataSet2\labels\train"  # 标签文件目录路径
# output_directory = r"D:\WH_WorkFiles\ProjectFile\03RPA\DataSet2\output"  # 输出目录路径


image_directory = r"D:\WH_WorkFiles\ProjectFile\03RPA\ocrerror"  # 图片目录路径
label_directory = r"D:\WH_WorkFiles\ProjectFile\03RPA\ocrerror"  # 标签文件目录路径
output_directory = r"D:\WH_WorkFiles\ProjectFile\03RPA\ocrerror\output2"  # 输出目录路径

height1_increase = 4  # 0 类别框增加高度
height2_increase = 0  # 1 类别框增加高度

isshow=True
addheight=True
# 增加注释框的高度
draw_yolo_bboxes(image_directory, label_directory, output_directory, isshow,height1_increase,height2_increase,addheight)
