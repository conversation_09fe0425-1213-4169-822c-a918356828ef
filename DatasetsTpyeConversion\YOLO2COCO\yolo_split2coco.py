import json
import os
import cv2
import shutil
from datetime import datetime


class YoloSplitToCocoDataset:
    """处理已分割的YOLO数据集转换为COCO格式"""
    
    def __init__(self, yolo_root_dir, output_dir, class_names):
        """
        初始化转换器
        
        :param yolo_root_dir: YOLO数据集根目录，包含images和labels文件夹
        :param output_dir: 输出COCO数据集目录
        :param class_names: 类别名称列表
        
        期望的YOLO数据集结构：
        yolo_root_dir/
        ├── images/
        │   ├── train/
        │   └── val/
        └── labels/
            ├── train/
            └── val/
        """
        self.yolo_root_dir = yolo_root_dir
        self.output_dir = output_dir
        self.class_names = class_names
        
        # 定义路径
        self.yolo_images_dir = os.path.join(yolo_root_dir, 'images')
        self.yolo_labels_dir = os.path.join(yolo_root_dir, 'labels')
        
        # 验证输入结构
        self._validate_yolo_structure()
        
        # 创建输出目录结构
        self._create_coco_structure()
    
    def _validate_yolo_structure(self):
        """验证YOLO数据集结构"""
        required_dirs = [
            self.yolo_images_dir,
            self.yolo_labels_dir,
            os.path.join(self.yolo_images_dir, 'train'),
            os.path.join(self.yolo_images_dir, 'val'),
            os.path.join(self.yolo_labels_dir, 'train'),
            os.path.join(self.yolo_labels_dir, 'val')
        ]
        
        missing_dirs = []
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                missing_dirs.append(dir_path)
        
        if missing_dirs:
            raise ValueError(f"YOLO数据集结构不完整，缺少以下目录：\\n" + "\\n".join(missing_dirs))
        
        print("✅ YOLO数据集结构验证通过")
    
    def _create_coco_structure(self):
        """创建COCO数据集目录结构"""
        self.coco_dirs = {
            'root': self.output_dir,
            'images': os.path.join(self.output_dir, 'images'),
            'annotations': os.path.join(self.output_dir, 'annotations'),
            'train_images': os.path.join(self.output_dir, 'images', 'train'),
            'val_images': os.path.join(self.output_dir, 'images', 'val')
        }
        
        # 创建所有目录
        for dir_path in self.coco_dirs.values():
            os.makedirs(dir_path, exist_ok=True)
        
        print(f"✅ 创建COCO数据集目录结构: {self.output_dir}")
    
    def _get_split_files(self, split_name):
        """获取指定分割的图片文件列表"""
        images_dir = os.path.join(self.yolo_images_dir, split_name)
        labels_dir = os.path.join(self.yolo_labels_dir, split_name)
        
        image_files = []
        supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        
        for file in os.listdir(images_dir):
            if any(file.lower().endswith(ext) for ext in supported_formats):
                # 检查是否有对应的标注文件
                label_file = os.path.splitext(file)[0] + '.txt'
                label_path = os.path.join(labels_dir, label_file)
                if os.path.exists(label_path):
                    image_files.append(file)
                else:
                    print(f"⚠️  警告：{split_name} 集中图片 {file} 没有对应的标注文件")
        
        return sorted(image_files)
    
    def _create_coco_annotation_structure(self, split_name):
        """创建COCO标注文件结构"""
        return {
            "info": {
                "description": f"YOLO to COCO conversion - {split_name} set",
                "version": "1.0",
                "year": datetime.now().year,
                "contributor": "Auto-generated",
                "date_created": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            },
            "licenses": [
                {
                    "id": 1,
                    "name": "Unknown",
                    "url": ""
                }
            ],
            "images": [],
            "annotations": [],
            "categories": []
        }
    
    def _add_categories(self, coco_data):
        """添加类别信息"""
        for i, class_name in enumerate(self.class_names):
            coco_data["categories"].append({
                "id": i,
                "name": class_name,
                "supercategory": "object"
            })

    def _scan_empty_annotation_files(self, splits):
        """扫描并报告空的标注文件"""
        print("\\n🔍 扫描空标注文件...")

        total_empty_files = 0
        total_missing_files = 0

        for split_name in splits:
            split_images_dir = os.path.join(self.yolo_images_dir, split_name)
            split_labels_dir = os.path.join(self.yolo_labels_dir, split_name)

            if not os.path.exists(split_images_dir) or not os.path.exists(split_labels_dir):
                continue

            # 获取所有图片文件
            image_files = []
            supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']

            for file in os.listdir(split_images_dir):
                if any(file.lower().endswith(ext) for ext in supported_formats):
                    image_files.append(file)

            empty_files = []
            missing_files = []

            for image_file in image_files:
                label_file = os.path.splitext(image_file)[0] + '.txt'
                label_path = os.path.join(split_labels_dir, label_file)

                if not os.path.exists(label_path):
                    missing_files.append(label_file)
                elif os.path.getsize(label_path) == 0:
                    empty_files.append(label_file)
                else:
                    # 检查是否只包含空行
                    try:
                        with open(label_path, 'r') as f:
                            lines = f.readlines()
                            non_empty_lines = [line.strip() for line in lines if line.strip()]
                            if not non_empty_lines:
                                empty_files.append(label_file)
                    except Exception:
                        pass

            if empty_files or missing_files:
                print(f"   📂 {split_name} 集:")
                if empty_files:
                    print(f"      - 空标注文件: {len(empty_files)} 个")
                    total_empty_files += len(empty_files)
                if missing_files:
                    print(f"      - 缺失标注文件: {len(missing_files)} 个")
                    total_missing_files += len(missing_files)

        if total_empty_files > 0 or total_missing_files > 0:
            print(f"\\n📊 扫描结果汇总:")
            if total_empty_files > 0:
                print(f"   - 总空标注文件: {total_empty_files} 个")
            if total_missing_files > 0:
                print(f"   - 总缺失标注文件: {total_missing_files} 个")
            print("   ℹ️  这些文件在转换过程中将被跳过")
        else:
            print("   ✅ 未发现空标注文件或缺失标注文件")

        return total_empty_files, total_missing_files
    
    def _process_split(self, split_name, task_type="detection"):
        """处理单个数据集分割"""
        print(f"\\n📂 处理 {split_name} 集...")

        # 获取文件列表
        image_files = self._get_split_files(split_name)
        print(f"   找到 {len(image_files)} 张有效图片")

        if len(image_files) == 0:
            print(f"⚠️  警告：{split_name} 集没有找到有效图片")
            return None

        # 创建COCO标注结构
        coco_data = self._create_coco_annotation_structure(split_name)
        self._add_categories(coco_data)

        # 源目录和目标目录
        src_images_dir = os.path.join(self.yolo_images_dir, split_name)
        src_labels_dir = os.path.join(self.yolo_labels_dir, split_name)
        dst_images_dir = self.coco_dirs[f'{split_name}_images']

        annotation_id = 0

        # 统计信息
        empty_files_count = 0
        missing_files_count = 0
        images_without_annotations = 0
        
        for image_id, image_file in enumerate(image_files):
            # 复制图片
            src_image_path = os.path.join(src_images_dir, image_file)
            dst_image_path = os.path.join(dst_images_dir, image_file)
            shutil.copy2(src_image_path, dst_image_path)
            
            # 读取图片尺寸
            img = cv2.imread(src_image_path)
            if img is None:
                print(f"⚠️  警告：无法读取图片 {image_file}")
                continue
            
            height, width = img.shape[:2]
            
            # 添加图片信息
            coco_data["images"].append({
                "id": image_id,
                "width": width,
                "height": height,
                "file_name": image_file,
                "license": 1,
                "date_captured": ""
            })
            
            # 处理标注
            label_file = os.path.splitext(image_file)[0] + '.txt'
            label_path = os.path.join(src_labels_dir, label_file)

            # 检查标注文件是否存在和是否为空
            if not os.path.exists(label_path):
                print(f"⚠️  警告：标注文件不存在 {label_file}")
                missing_files_count += 1
                continue

            # 检查文件是否为空
            if os.path.getsize(label_path) == 0:
                print(f"ℹ️  信息：跳过空标注文件 {label_file} (对应图片: {image_file})")
                empty_files_count += 1
                continue

            # 读取并处理标注文件
            annotation_count_for_image = 0
            with open(label_path, 'r') as f:
                lines = f.readlines()

                # 检查文件内容是否全为空行
                non_empty_lines = [line.strip() for line in lines if line.strip()]
                if not non_empty_lines:
                    print(f"ℹ️  信息：跳过仅含空行的标注文件 {label_file} (对应图片: {image_file})")
                    empty_files_count += 1
                    continue

                for line_num, line in enumerate(lines, 1):
                    line = line.strip()
                    if not line:
                        continue

                    parts = line.split()
                    if len(parts) < 5:
                        print(f"⚠️  警告：{label_file} 第{line_num}行格式错误")
                        continue

                    try:
                        class_id = int(parts[0])

                        # 验证类别ID
                        if class_id >= len(self.class_names):
                            print(f"⚠️  警告：{label_file} 第{line_num}行类别ID {class_id} 超出范围")
                            continue

                        if task_type == "detection":
                            annotation = self._process_detection_annotation(
                                parts, class_id, width, height, annotation_id, image_id
                            )
                        else:  # segmentation
                            annotation = self._process_segmentation_annotation(
                                parts, class_id, width, height, annotation_id, image_id
                            )

                        if annotation:
                            coco_data["annotations"].append(annotation)
                            annotation_id += 1
                            annotation_count_for_image += 1

                    except (ValueError, IndexError) as e:
                        print(f"⚠️  警告：{label_file} 第{line_num}行数据错误: {e}")
                        continue

            # 如果图片有对应的标注文件但没有有效标注，给出信息
            if annotation_count_for_image == 0:
                print(f"ℹ️  信息：图片 {image_file} 的标注文件 {label_file} 没有有效标注")
                images_without_annotations += 1
        
        # 保存标注文件
        annotation_file = os.path.join(self.coco_dirs['annotations'], f'{split_name}.json')
        with open(annotation_file, 'w', encoding='utf-8') as f:
            json.dump(coco_data, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ {split_name} 集处理完成:")
        print(f"      - 图片数量: {len(coco_data['images'])}")
        print(f"      - 标注数量: {len(coco_data['annotations'])}")
        print(f"      - 标注文件: {annotation_file}")

        # 显示统计信息
        if empty_files_count > 0 or missing_files_count > 0 or images_without_annotations > 0:
            print(f"   📊 处理统计:")
            if empty_files_count > 0:
                print(f"      - 空标注文件: {empty_files_count} 个")
            if missing_files_count > 0:
                print(f"      - 缺失标注文件: {missing_files_count} 个")
            if images_without_annotations > 0:
                print(f"      - 无有效标注的图片: {images_without_annotations} 张")

        return coco_data
    
    def _process_detection_annotation(self, parts, class_id, width, height, annotation_id, image_id):
        """处理检测标注"""
        if len(parts) < 5:
            return None
        
        cx, cy, w, h = map(float, parts[1:5])
        
        # YOLO格式转COCO格式
        x = (cx - w/2) * width
        y = (cy - h/2) * height
        bbox_width = w * width
        bbox_height = h * height
        
        # 边界检查
        x = max(0, x)
        y = max(0, y)
        bbox_width = min(bbox_width, width - x)
        bbox_height = min(bbox_height, height - y)
        
        if bbox_width <= 0 or bbox_height <= 0:
            return None
        
        return {
            "id": annotation_id,
            "image_id": image_id,
            "category_id": class_id,
            "bbox": [x, y, bbox_width, bbox_height],
            "area": bbox_width * bbox_height,
            "segmentation": [],
            "iscrowd": 0
        }
    
    def _process_segmentation_annotation(self, parts, class_id, width, height, annotation_id, image_id):
        """处理分割标注"""
        if len(parts) < 7:  # 至少需要类别 + 3个点
            return None
        
        coords = list(map(float, parts[1:]))
        if len(coords) % 2 != 0:
            return None
        
        # 转换为像素坐标
        segmentation = []
        for i in range(0, len(coords), 2):
            x = coords[i] * width
            y = coords[i+1] * height
            segmentation.extend([x, y])
        
        # 计算边界框
        x_coords = segmentation[::2]
        y_coords = segmentation[1::2]
        
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        
        bbox_width = x_max - x_min
        bbox_height = y_max - y_min
        
        if bbox_width <= 0 or bbox_height <= 0:
            return None
        
        return {
            "id": annotation_id,
            "image_id": image_id,
            "category_id": class_id,
            "bbox": [x_min, y_min, bbox_width, bbox_height],
            "area": bbox_width * bbox_height,
            "segmentation": [segmentation],
            "iscrowd": 0
        }
    
    def convert(self, task_type="detection", splits=None):
        """
        执行转换
        
        :param task_type: 任务类型，"detection" 或 "segmentation"
        :param splits: 要处理的分割列表，默认为 ['train', 'val']
        :return: 转换结果
        """
        if splits is None:
            splits = ['train', 'val']
        
        print("=" * 70)
        print("已分割YOLO数据集转COCO格式转换")
        print("=" * 70)
        print(f"📁 YOLO数据集: {self.yolo_root_dir}")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🏷️  类别数量: {len(self.class_names)}")
        print(f"🏷️  类别列表: {self.class_names}")
        print(f"🎯 任务类型: {task_type}")
        print(f"📊 处理分割: {splits}")

        # 预扫描空标注文件
        self._scan_empty_annotation_files(splits)

        results = {}
        total_images = 0
        total_annotations = 0
        
        # 处理每个分割
        for split_name in splits:
            # 检查分割是否存在
            split_images_dir = os.path.join(self.yolo_images_dir, split_name)
            split_labels_dir = os.path.join(self.yolo_labels_dir, split_name)
            
            if not os.path.exists(split_images_dir) or not os.path.exists(split_labels_dir):
                print(f"⚠️  跳过不存在的分割: {split_name}")
                continue
            
            result = self._process_split(split_name, task_type)
            if result:
                results[split_name] = result
                total_images += len(result['images'])
                total_annotations += len(result['annotations'])
        
        # 创建数据集信息文件
        self._create_dataset_info(results, task_type)
        
        print("\\n" + "=" * 70)
        print("🎉 转换完成！")
        print(f"📊 总计: {total_images} 张图片, {total_annotations} 个标注")
        print(f"📁 输出目录: {self.output_dir}")
        print("\\n📁 生成的COCO数据集结构:")
        print(f"{self.output_dir}/")
        print("├── images/")
        for split_name in results.keys():
            print(f"│   ├── {split_name}/")
        print("├── annotations/")
        for split_name in results.keys():
            print(f"│   ├── {split_name}.json")
        print("└── dataset_info.json")
        print("=" * 70)
        
        return results
    
    def _create_dataset_info(self, results, task_type):
        """创建数据集信息文件"""
        dataset_info = {
            "dataset_name": "YOLO_Split_to_COCO_conversion",
            "task_type": task_type,
            "creation_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "source_structure": "pre-split YOLO dataset",
            "classes": {
                "count": len(self.class_names),
                "names": self.class_names
            },
            "splits": {},
            "total_images": sum(len(data['images']) for data in results.values()),
            "total_annotations": sum(len(data['annotations']) for data in results.values()),
            "directory_structure": {
                "images": "images/",
                "annotations": "annotations/"
            }
        }
        
        for split_name, data in results.items():
            dataset_info["splits"][split_name] = {
                "image_count": len(data['images']),
                "annotation_count": len(data['annotations']),
                "annotation_file": f"annotations/{split_name}.json",
                "image_directory": f"images/{split_name}/"
            }
        
        info_file = os.path.join(self.output_dir, 'dataset_info.json')
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(dataset_info, f, indent=2, ensure_ascii=False)


# 便捷函数
def convert_split_yolo_to_coco(yolo_root_dir, output_dir, class_names, task_type="detection", splits=None):
    """
    转换已分割的YOLO数据集为COCO格式的便捷函数
    
    :param yolo_root_dir: YOLO数据集根目录
    :param output_dir: 输出COCO数据集目录
    :param class_names: 类别名称列表
    :param task_type: 任务类型，"detection" 或 "segmentation"
    :param splits: 要处理的分割列表，默认为 ['train', 'val']
    :return: 转换结果
    """
    converter = YoloSplitToCocoDataset(yolo_root_dir, output_dir, class_names)
    return converter.convert(task_type, splits)


# 使用示例
if __name__ == "__main__":
    # 配置参数
    YOLO_ROOT_DIR = r"E:\datasets\yolo_dataset"        # YOLO数据集根目录
    OUTPUT_DIR = r"E:\datasets\coco_dataset"           # 输出COCO数据集目录
    CLASS_NAMES = ["person", "car", "bicycle"]         # 类别名称列表
    TASK_TYPE = "detection"                             # "detection" 或 "segmentation"
    SPLITS = ["train", "val"]                           # 要处理的分割
    
    try:
        # 方法1: 使用类
        converter = YoloSplitToCocoDataset(YOLO_ROOT_DIR, OUTPUT_DIR, CLASS_NAMES)
        results = converter.convert(TASK_TYPE, SPLITS)
        
        # 方法2: 使用便捷函数
        # results = convert_split_yolo_to_coco(YOLO_ROOT_DIR, OUTPUT_DIR, CLASS_NAMES, TASK_TYPE, SPLITS)
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\\n请修改上面的配置参数后运行转换")