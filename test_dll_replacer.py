#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
DLL替换工具测试脚本
用于验证dll_replacer.py的基本功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

def create_test_environment():
    """创建测试环境"""
    print("正在创建测试环境...")
    
    # 创建临时目录
    test_dir = Path(tempfile.mkdtemp(prefix="dll_test_"))
    print(f"测试目录: {test_dir}")
    
    # 创建测试用的"DLL"文件（实际是文本文件）
    new_dll = test_dir / "new_halcondotnet.dll"
    old_dll1 = test_dir / "app1" / "halcondotnet.dll"
    old_dll2 = test_dir / "app2" / "halcondotnet.dll"
    
    # 创建目录
    old_dll1.parent.mkdir(parents=True)
    old_dll2.parent.mkdir(parents=True)
    
    # 创建文件内容
    new_dll.write_text("新版本DLL内容 v2.0", encoding='utf-8')
    old_dll1.write_text("旧版本DLL内容 v1.0", encoding='utf-8')
    old_dll2.write_text("旧版本DLL内容 v1.5", encoding='utf-8')
    
    print(f"创建新DLL: {new_dll}")
    print(f"创建旧DLL1: {old_dll1}")
    print(f"创建旧DLL2: {old_dll2}")
    
    return test_dir, new_dll, [old_dll1, old_dll2]

def test_dry_run(test_dir, new_dll):
    """测试预览模式"""
    print("\n=== 测试预览模式 ===")
    
    cmd = f'python dll_replacer.py --new-dll "{new_dll}" --search-paths "{test_dir}" --dry-run --yes'
    print(f"执行命令: {cmd}")
    
    result = os.system(cmd)
    return result == 0

def test_actual_replacement(test_dir, new_dll):
    """测试实际替换"""
    print("\n=== 测试实际替换 ===")
    
    cmd = f'python dll_replacer.py --new-dll "{new_dll}" --search-paths "{test_dir}" --yes --verbose'
    print(f"执行命令: {cmd}")
    
    result = os.system(cmd)
    return result == 0

def verify_replacement(test_dir, new_dll, old_dlls):
    """验证替换结果"""
    print("\n=== 验证替换结果 ===")
    
    new_content = Path(new_dll).read_text(encoding='utf-8')
    
    for old_dll in old_dlls:
        if old_dll.exists():
            old_content = old_dll.read_text(encoding='utf-8')
            if old_content == new_content:
                print(f"✓ {old_dll} 替换成功")
            else:
                print(f"✗ {old_dll} 替换失败")
                return False
        else:
            print(f"✗ {old_dll} 文件不存在")
            return False
    
    return True

def check_backup_files(test_dir):
    """检查备份文件"""
    print("\n=== 检查备份文件 ===")
    
    backup_dir = test_dir / "dll_backup"
    if backup_dir.exists():
        backup_files = list(backup_dir.rglob("*.dll"))
        print(f"找到 {len(backup_files)} 个备份文件:")
        for backup_file in backup_files:
            print(f"  - {backup_file}")
        return len(backup_files) > 0
    else:
        print("未找到备份目录")
        return False

def cleanup_test_environment(test_dir):
    """清理测试环境"""
    print(f"\n清理测试环境: {test_dir}")
    try:
        shutil.rmtree(test_dir)
        print("清理完成")
    except Exception as e:
        print(f"清理失败: {e}")

def main():
    """主测试函数"""
    print("DLL替换工具测试开始")
    print("=" * 50)
    
    # 检查dll_replacer.py是否存在
    if not os.path.exists('dll_replacer.py'):
        print("错误: dll_replacer.py 文件不存在")
        return 1
    
    test_dir = None
    try:
        # 创建测试环境
        test_dir, new_dll, old_dlls = create_test_environment()
        
        # 测试预览模式
        if not test_dry_run(test_dir, new_dll):
            print("❌ 预览模式测试失败")
            return 1
        print("✅ 预览模式测试通过")
        
        # 测试实际替换
        if not test_actual_replacement(test_dir, new_dll):
            print("❌ 实际替换测试失败")
            return 1
        print("✅ 实际替换测试通过")
        
        # 验证替换结果
        if not verify_replacement(test_dir, new_dll, old_dlls):
            print("❌ 替换结果验证失败")
            return 1
        print("✅ 替换结果验证通过")
        
        # 检查备份文件
        if not check_backup_files(test_dir):
            print("❌ 备份文件检查失败")
            return 1
        print("✅ 备份文件检查通过")
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！DLL替换工具工作正常")
        
        return 0
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        return 1
    
    finally:
        # 清理测试环境
        if test_dir and os.path.exists(test_dir):
            cleanup_test_environment(test_dir)

if __name__ == '__main__':
    sys.exit(main())