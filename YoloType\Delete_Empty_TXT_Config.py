#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置版本：删除目录中空的txt文本文件
Config version: Delete empty txt files in directory

直接修改下面的配置参数即可使用
"""

import os
import glob

# ==================== 配置区域 ====================
# 在这里修改配置参数

# 目标目录路径 ("." 表示当前目录)
TARGET_DIRECTORY = "."

# 是否递归搜索子目录
RECURSIVE_SEARCH = True

# 是否只预览不删除 (True=只预览, False=实际删除)
PREVIEW_ONLY = False

# 是否需要确认才删除 (True=需要确认, False=直接删除)
REQUIRE_CONFIRMATION = True

# ==================== 配置区域结束 ====================


def delete_empty_txt_files():
    """删除空的txt文件"""
    
    print("=" * 60)
    print("删除空txt文件工具 - 配置版本")
    print("=" * 60)
    print(f"目标目录: {os.path.abspath(TARGET_DIRECTORY)}")
    print(f"递归搜索: {'是' if RECURSIVE_SEARCH else '否'}")
    print(f"预览模式: {'是' if PREVIEW_ONLY else '否'}")
    print(f"需要确认: {'是' if REQUIRE_CONFIRMATION else '否'}")
    print("-" * 60)
    
    # 检查目录是否存在
    if not os.path.exists(TARGET_DIRECTORY):
        print(f"错误: 目录 '{TARGET_DIRECTORY}' 不存在")
        return
    
    if not os.path.isdir(TARGET_DIRECTORY):
        print(f"错误: '{TARGET_DIRECTORY}' 不是一个目录")
        return
    
    # 搜索txt文件
    if RECURSIVE_SEARCH:
        pattern = os.path.join(TARGET_DIRECTORY, "**", "*.txt")
        txt_files = glob.glob(pattern, recursive=True)
    else:
        pattern = os.path.join(TARGET_DIRECTORY, "*.txt")
        txt_files = glob.glob(pattern)
    
    print(f"找到 {len(txt_files)} 个txt文件")
    
    # 查找空文件
    empty_files = []
    for txt_file in txt_files:
        try:
            if os.path.getsize(txt_file) == 0:
                empty_files.append(txt_file)
                print(f"发现空文件: {txt_file}")
        except OSError as e:
            print(f"无法访问文件 {txt_file}: {e}")
    
    if not empty_files:
        print("未找到空的txt文件")
        return
    
    print(f"\n共找到 {len(empty_files)} 个空txt文件")
    
    # 预览模式
    if PREVIEW_ONLY:
        print("\n=== 预览模式 - 以下文件将被删除 ===")
        for file_path in empty_files:
            print(f"  - {file_path}")
        print(f"\n预览完成，共 {len(empty_files)} 个空文件")
        return
    
    # 确认删除
    if REQUIRE_CONFIRMATION:
        confirm = input(f"\n确认删除这 {len(empty_files)} 个空txt文件吗? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("操作已取消")
            return
    
    # 执行删除
    deleted_count = 0
    print("\n开始删除...")
    
    for txt_file in empty_files:
        try:
            os.remove(txt_file)
            print(f"已删除: {txt_file}")
            deleted_count += 1
        except OSError as e:
            print(f"删除失败 {txt_file}: {e}")
    
    print(f"\n删除完成！共删除了 {deleted_count} 个空txt文件")


if __name__ == "__main__":
    delete_empty_txt_files()