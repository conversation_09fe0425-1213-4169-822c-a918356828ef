# YOLO到COCO格式转换工具

本工具提供了将YOLO格式数据集转换为COCO格式的完整解决方案，支持检测和分割两种任务类型。

## 🎯 推荐使用方案

### 情况1：已分割的YOLO数据集 → 使用 `convert_split_yolo.py` ⭐

**最推荐使用**，适用于已经分割好的YOLO数据集（有独立的train/val文件夹）。

**输入YOLO数据集结构：**
```
yolo_dataset/
├── images/
│   ├── train/          # 训练集图片
│   └── val/            # 验证集图片
└── labels/
    ├── train/          # 训练集标注txt文件
    └── val/            # 验证集标注txt文件
```

**输出COCO数据集结构：**
```
coco_dataset/
├── images/
│   ├── train/          # 训练集图片（复制）
│   └── val/            # 验证集图片（复制）
├── annotations/
│   ├── train.json      # 训练集COCO标注
│   └── val.json        # 验证集COCO标注
└── dataset_info.json   # 数据集信息
```

### 情况2：单一文件夹YOLO数据集 → 使用 `create_coco_dataset.py`

适用于单一文件夹的YOLO数据集，可以自动分割为train/val。

**输入YOLO数据集结构：**
```
yolo_dataset/
├── images/             # 所有图片
└── labels/             # 所有标注txt文件
```

## 📁 文件说明

### 🌟 已分割数据集转换（最推荐）
- **`convert_split_yolo.py`**: 已分割数据集转换脚本（**主推荐使用**）
- `yolo_split2coco.py`: 已分割YOLO数据集转换器（核心引擎）

### 🔄 单文件夹数据集转换
- **`create_coco_dataset.py`**: 完整数据集创建脚本（推荐使用）
- `yolo2coco_dataset.py`: 完整COCO数据集生成器（自动分割）

### 🛠️ 基础转换工具
- `yolo2coco_config.py`: 配置版转换脚本
- `example_usage.py`: 基础转换使用示例
- `yolo2coco.py`: 命令行交互版转换脚本
- `yolo2coco_simple.py`: 简化版转换脚本

## 🚀 快速开始

### 方法1：已分割YOLO数据集转换（推荐）

1. **打开配置文件**
   ```bash
   # 编辑 convert_split_yolo.py
   ```

2. **修改配置参数**
   ```python
   # YOLO数据集根目录（包含images和labels文件夹）
   YOLO_ROOT_DIR = r"E:\datasets\yolo_dataset"
   
   # 输出COCO数据集目录
   OUTPUT_DIR = r"E:\datasets\coco_dataset"
   
   # 类别名称列表（按照YOLO标注文件中的类别ID顺序）
   CLASS_NAMES = [
       "person",       # 类别ID: 0
       "car",          # 类别ID: 1
       "bicycle",      # 类别ID: 2
       # 添加更多类别...
   ]
   
   # 任务类型
   TASK_TYPE = "detection"  # "detection" 或 "segmentation"
   
   # 要处理的数据集分割
   SPLITS = ["train", "val"]  # 可以包含 "train", "val", "test" 等
   ```

3. **运行转换**
   ```bash
   python convert_split_yolo.py
   ```

### 方法2：单文件夹数据集转换

1. **打开配置文件**
   ```bash
   # 编辑 create_coco_dataset.py
   ```

2. **修改配置参数**
   ```python
   # 输入路径配置
   YOLO_IMAGES_DIR = r"E:\datasets\yolo\images"       # YOLO图片文件夹
   YOLO_LABELS_DIR = r"E:\datasets\yolo\labels"       # YOLO标注文件夹
   
   # 输出路径配置
   OUTPUT_DIR = r"E:\datasets\coco_dataset"           # 输出COCO数据集目录
   
   # 类别配置
   CLASS_NAMES = ["person", "car", "bicycle"]         # 类别名称列表
   
   # 数据集分割比例配置
   TRAIN_RATIO = 0.8   # 训练集比例 (80%)
   VAL_RATIO = 0.2     # 验证集比例 (20%)
   TEST_RATIO = 0.0    # 测试集比例 (0%, 可选)
   
   # 任务类型配置
   TASK_TYPE = "detection"  # "detection" 或 "segmentation"
   ```

3. **运行转换**
   ```bash
   python create_coco_dataset.py
   ```

## 📊 数据格式说明

### YOLO格式

**检测任务 (Detection):**
```
class_id center_x center_y width height
```
- 所有坐标都是归一化的 (0-1之间)
- center_x, center_y: 边界框中心点坐标
- width, height: 边界框宽度和高度

**分割任务 (Segmentation):**
```
class_id x1 y1 x2 y2 x3 y3 ... xn yn
```
- 所有坐标都是归一化的 (0-1之间)
- x1 y1, x2 y2, ...: 多边形顶点坐标

### COCO格式

- JSON文件包含images、annotations、categories三个主要部分
- bbox格式: [x, y, width, height] (左上角坐标 + 宽高)
- segmentation格式: [[x1, y1, x2, y2, ...]] (多边形顶点坐标列表)

## 🔧 依赖库

```bash
pip install opencv-python
```

## ⚠️ 注意事项

1. **图片和标注文件名对应**: 图片文件名和标注文件名必须一致（除了扩展名）
2. **支持的图片格式**: jpg, jpeg, png, bmp, tiff, webp
3. **坐标范围**: YOLO格式的坐标必须在0-1之间
4. **类别ID**: 从0开始，必须连续
5. **文件编码**: 建议使用UTF-8编码
6. **数据集结构**: 确保YOLO数据集结构正确，特别是已分割数据集的train/val文件夹

## 📈 输出示例

转换完成后会显示：
```
✅ 转换成功完成！

📊 转换统计:
  train 集:  800 张图片,  1250 个标注
    val 集:  200 张图片,   310 个标注
   总计: 1000 张图片,  1560 个标注

📁 生成的COCO数据集结构:
coco_dataset/
├── images/
│   ├── train/
│   └── val/
├── annotations/
│   ├── train.json
│   └── val.json
└── dataset_info.json

🎉 COCO数据集已成功创建在: E:\datasets\coco_dataset
```

## ❓ 常见问题

### Q1: 我的YOLO数据集已经分割好了，应该用哪个脚本？
**A:** 使用 `convert_split_yolo.py`，这是专门为已分割数据集设计的。

### Q2: 我的YOLO数据集在单一文件夹中，怎么办？
**A:** 使用 `create_coco_dataset.py`，它会自动分割数据集。

### Q3: 转换后的COCO数据集可以直接用于训练吗？
**A:** 是的，生成的COCO数据集完全符合标准格式，可以直接用于各种深度学习框架。

### Q4: 支持哪些图片格式？
**A:** 支持 jpg, jpeg, png, bmp, tiff, webp 等常见格式。

### Q5: 如何验证转换结果？
**A:** 脚本会自动验证生成的数据集结构和JSON格式，并提供详细的统计信息。

### Q6: 转换过程中出现错误怎么办？
**A:** 脚本包含完整的错误处理，会显示具体的错误信息和建议。常见问题包括：
- 检查文件路径是否正确
- 确保YOLO标注文件格式正确
- 验证类别ID是否在范围内
- 确保坐标值在0-1之间

### Q7: 可以只转换部分数据集吗？
**A:** 可以，在 `SPLITS` 参数中指定要转换的分割，如只转换训练集：`SPLITS = ["train"]`

### Q8: 转换后的文件很大，正常吗？
**A:** 是的，COCO格式的JSON文件包含详细的标注信息，文件较大是正常的。

## 🔍 验证转换结果

转换完成后，可以使用以下方法验证结果：

1. **检查目录结构**: 确保生成了正确的文件夹和文件
2. **查看dataset_info.json**: 包含数据集的详细统计信息
3. **验证JSON格式**: 脚本会自动验证JSON文件的格式正确性
4. **检查图片数量**: 确保图片数量与原始数据集一致

## 📞 技术支持

如果遇到问题，请检查：
1. YOLO数据集结构是否正确
2. 配置参数是否设置正确
3. 文件路径是否存在
4. 类别名称列表是否与标注文件匹配

## 🎉 完成

现在您可以使用生成的COCO数据集进行模型训练了！生成的数据集完全符合COCO标准格式，可以直接用于：
- YOLOv5/YOLOv8 训练
- Detectron2 训练
- MMDetection 训练
- 其他支持COCO格式的深度学习框架