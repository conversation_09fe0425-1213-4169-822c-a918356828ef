@echo off
chcp 65001 >nul 2>&1

echo ================================================
echo          DLL Replacer Simple Start
echo ================================================
echo.

REM Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python not found
    pause
    exit /b 1
)

REM Check script
if not exist "dll_replacer.py" (
    echo Error: dll_replacer.py not found
    pause
    exit /b 1
)

REM Get DLL path
set /p NEW_DLL="Enter new DLL file path: "
if "%NEW_DLL%"=="" (
    echo Error: DLL path cannot be empty
    pause
    exit /b 1
)

REM Check DLL exists
if not exist "%NEW_DLL%" (
    echo Error: DLL file not found: %NEW_DLL%
    pause
    exit /b 1
)

echo.
echo New DLL: %NEW_DLL%
echo.

echo Running preview mode first...
echo.
python dll_replacer.py --new-dll "%NEW_DLL%" --dry-run --verbose

echo.
echo ================================================
echo Preview completed. Do you want to proceed?
echo ================================================
pause

echo.
echo Running actual replacement...
python dll_replacer.py --new-dll "%NEW_DLL%" --verbose

echo.
echo Done!
pause 