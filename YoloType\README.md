# 数据集处理工具集

本仓库包含一系列用于处理、转换和管理数据集（特别是YOLO格式数据集）的Python脚本。下面是每个脚本的功能简介和使用说明。

## 目录

1. [图像处理脚本](#图像处理脚本)
2. [标签处理脚本](#标签处理脚本)
3. [数据集管理脚本](#数据集管理脚本)
4. [数据集转换脚本](#数据集转换脚本)
5. [OCR工具](#ocr工具)

## 图像处理脚本

### CropImageDrawLabel.py

**功能**：裁剪图像和对应点的YOLO格式的标签，同时可以在裁剪后的图像上绘制边界框。

**主要特点**：
- 读取原始图像和YOLO格式标签
- 裁剪图像到指定区域
- 调整标签坐标以匹配裁剪后的图像
- 可选择在裁剪后的图像上绘制边界框
- 输出裁剪后的图像和调整后的标签

**使用方法**：
修改脚本中的路径和参数，然后运行脚本。

### Drawlabel.py

**功能**：在图像上绘制YOLO格式标签的边界框，便于可视化检查标注质量。

**主要特点**：
- 读取图像和对应的YOLO格式标签文件
- 在图像上绘制边界框和类别ID
- 将可视化后的边界框图像保存到指定目录
- 识别并复制没有标签的图像到单独的目录

**使用方法**：
修改脚本中的路径和参数，然后运行脚本。

### Scale_Image.py

**功能**：缩放图像到指定尺寸。

**使用方法**：
修改脚本中的路径和尺寸参数，然后运行脚本。

## 标签处理脚本

### Delete_Label.py

**功能**：删除指定类别的标签。

**主要特点**：
- 遍历标签文件
- 删除指定类别ID的标签行
- 保存修改后的标签文件

**使用方法**：
修改脚本中的路径和要删除的类别ID，然后运行脚本。

### Delete_NOIMageLabel.py

**功能**：删除没有对应图像文件的标签文件。

**主要特点**：
- 比较图像目录和标签目录中的文件
- 删除没有对应图像的标签文件

**使用方法**：
```python
image_directory = '图像目录路径'
label_directory = '标签目录路径'
clean_yolo_labels(image_directory, label_directory)
```

### ReplaceLabelNum.py

**功能**：替换标签文件中的类别ID。

**主要特点**：
- 读取标签文件
- 将指定的类别ID替换为新的ID
- 保存修改后的标签文件

**使用方法**：
修改脚本中的路径和类别ID映射，然后运行脚本。

### RestoreLabels.py

**功能**：将裁剪图像的标签还原为原始图像上的标签。

**主要特点**：
- 读取裁剪图像的标签
- 根据裁剪信息将标签还原到原始图像上的位置
- 保存还原后的标签
- 可选择在原始图像上绘制还原后的边界框

**使用方法**：
修改脚本中的路径和裁剪信息参数，然后运行脚本。

### LabelInfo.py

**功能**：统计标签文件中各个类别的分布情况。

**主要特点**：
- 遍历标签文件
- 统计每个类别的出现次数
- 输出统计结果

**使用方法**：
修改脚本中的路径和参数，然后运行脚本。

### Add_Height.py

**功能**：对YOLO格式的标签框增加或减少高度信息（针对特定场景）。

**主要特点**：
- 根据不同类别ID对边界框应用不同的高度调整量
- 支持增加模式和减少模式
- 自动调整边界框中心点坐标
- 保持宽度不变
- 生成可视化结果，显示调整后的边界框
- 输出调整后的YOLO格式标签

**使用方法**：
修改脚本中的路径和参数，然后运行脚本。主要参数包括：
```python
image_directory = "图像目录路径"
label_directory = "标签目录路径"
output_directory = "输出目录路径"
height1_increase = 4  # 类别ID为0的框增加/减少的高度
height2_increase = 0  # 类别ID为1的框增加/减少的高度
addheight = True      # True为增加高度，False为减少高度
```

## 数据集管理脚本

### SeparateUpDownFiles.py

**功能**：将包含"Up"和"Down"的文件分别复制或移动到不同的目录。

**主要特点**：
- 扫描源目录中的文件
- 检查文件名是否包含"Up"或"Down"关键字
- 将文件复制或移动到对应的目标目录

**使用方法**：
```
python SeparateUpDownFiles.py --source 源目录 --up-dir Up文件目录 --down-dir Down文件目录 [--move]
```
参数说明：
- `--source`：源文件目录路径
- `--up-dir`：存放包含'Up'的文件的目录
- `--down-dir`：存放包含'Down'的文件的目录
- `--move`：如果指定了此参数，则移动文件而不是复制

### RenameAndMerged_With_Timestamp.py

**功能**：合并多个文件夹中的文件，并按统一格式重命名（包含时间戳）。

**主要特点**：
- 读取多个源文件夹中的所有文件
- 使用统一的时间戳和序号重命名文件
- 将重命名后的文件复制到目标目录

**使用方法**：
修改脚本中的源目录和目标目录参数，然后运行脚本。

### SpiltDataSet.py

**功能**：将数据集分割为训练集和验证集。

**主要特点**：
- 随机选择一部分文件作为验证集
- 将剩余文件作为训练集
- 复制文件到对应的目录

**使用方法**：
修改脚本中的路径和分割比例参数，然后运行脚本。

### ResetImageName.py

**功能**：重置图像和对应标签文件的名称，使用统一的命名格式。

**使用方法**：
修改脚本中的路径和参数，然后运行脚本。

### ListFilePaths.py

**功能**：列出指定目录及其子目录中的所有文件路径，并保存到文本文件。

**使用方法**：
```
python ListFilePaths.py [目录路径] [输出文件路径]
```
如果不指定参数，将使用脚本中的默认值。

### CopyText.py

**功能**：复制文本文件内容。

**使用方法**：
修改脚本中的路径和参数，然后运行脚本。

## 数据集转换脚本

### ConvertToSegmentation.py

**功能**：将YOLO目标检测格式(边界框)的标注转换为分割格式标注

**主要特点**：
- 读取YOLO格式的目标检测标注
- 将边界框的四个角点坐标转换为多边形分割标注
- 输出格式为每行：class_id x1 y1 x2 y2 x3 y3 x4 y4
- 角点按照左上、右上、右下、左下顺序排列

**使用方法**：
修改脚本中的路径参数，包括图片目录、标注目录和输出目录，然后运行脚本。
```python
# 例如:
image_dir = "images/val"
label_dir = "labels/val"
output_dir = "labels_seg/val"
```

### YOLO2CroppedImage.py

**功能**：将标注好的YOLO数据转换成裁剪后对应图像的数据

**主要特点**：
- 使用Halcon样式的裁剪坐标 [row1, col1, row2, col2] 裁剪图像
- 将YOLO格式的标注映射到裁剪后的图像坐标系中
- 支持处理已裁剪的图像
- 可选择生成可视化结果
- 支持读取原始图像对应的文本标注并映射到裁剪后的图像

**使用方法**：
修改脚本中的路径和参数，然后运行脚本。主要参数包括：
```python
# 图像和标签目录
IMAGES_DIR = "图像目录"
LABELS_DIR = "YOLO标注文件目录"

# Halcon裁剪坐标 [row1, col1, row2, col2]
CROP_COORDS = [300, 15, 400, 470]

# 输出目录
OUTPUT_DIR = "输出目录"

# 可选参数
TEXT_FILE = ""  # 文本标注文件路径，如果有的话
VISUAL = True   # 是否生成可视化结果
```

### DatasetsTpyeConversion/labelme2yolo.py

**功能**：将LabelMe标注工具生成的JSON格式标注转换为YOLO格式。

**主要特点**：
- 支持目标框(det)和实例分割(seg)标签的转换
- 读取LabelMe的JSON文件
- 提取边界框或多边形坐标
- 转换为YOLO格式的标签文件

**使用方法**：
```python
# 目标框标签转换
class_name = ['类别1', '类别2', ...]  # 类别列表
json_dir = 'JSON文件目录'
labels_dir = 'YOLO标签输出目录'
labelme2yolo_det(class_name, json_dir, labels_dir)

# 分割标签转换
labelme2yolo_seg(class_name, json_dir, labels_dir)
```

## OCR工具

### HalconOCR2PPOCR

**功能**：将Halcon OCR标注文件转换为PaddleOCR (PPOCR)格式的标注文件

**主要特点**：
- 支持将Halcon OCR旋转矩形框转换为PPOCR的四点多边形格式
- 自动生成检测（det）和识别（rec）两种格式的标注文件
- 自动划分训练集、验证集和测试集（比例可调整）

**使用方法**：
有两种使用方式：

1. 直接运行脚本
```bash
python convert_halcon_to_ppocr.py --input HalconOCR.json --output output_dir
```

参数说明：
- `--input` 或 `-i`: Halcon OCR JSON标注文件路径
- `--output` 或 `-o`: 输出目录路径，将在此目录下生成det和rec两个子目录
- `--config` 或 `-c`: 可选，指定配置文件路径

2. 使用配置文件
编辑`config.json`文件，设置相应参数：
```json
{
    "input": "HalconOCR.json",
    "output": "ppocr_output",
    "train_ratio": 0.8,
    "val_ratio": 0.1,
    "test_ratio": 0.1,
    "random_seed": 42,
    "crop_image": false,
    "crop_dir": "crops"
}
```

然后运行：
```bash
python convert_halcon_to_ppocr.py --config config.json
```

**输出说明**：
转换后会在指定的输出目录下生成以下文件：
```
output_dir/
├── det/
│   ├── train.txt
│   ├── val.txt
│   └── test.txt
└── rec/
    ├── train.txt
    ├── val.txt
    └── test.txt
```

## YOLODatasetCreator.py

### 功能介绍

`YOLODatasetCreator.py` 是一个用于创建符合YOLO格式的数据集工具，可以将图像和标注文件按比例划分为训练集和验证集。

### 生成的数据集结构

```
output_dir/
├── images/
│   ├── train/ (训练集图像)
│   └── val/ (验证集图像)
└── labels/
    ├── train/ (训练集标注)
    └── val/ (验证集标注)
```

### 使用方法

#### 方法一：直接运行脚本

1. 修改脚本底部的参数设置
2. 运行脚本：`python YOLODatasetCreator.py`

```python
# 参数设置示例
create_yolo_dataset(
    images_dir=r"C:\Users\<USER>\Desktop\images",  # 原始图像目录
    labels_dir=r"C:\Users\<USER>\Desktop\labels",  # 原始标注目录
    output_dir=r"C:\Users\<USER>\Desktop\dataset", # 输出目录
    val_ratio=0.2,                                   # 验证集比例
    img_ext=['.jpg', '.jpeg', '.png', '.bmp'],       # 支持多种图像扩展名
    label_ext='.txt',                                # 标注文件扩展名
    seed=42                                          # 随机种子
)
```

#### 方法二：作为模块导入到其他代码中

```python
from YOLODatasetCreator import create_yolo_dataset

create_yolo_dataset(
    images_dir=r"C:\path\to\images",  
    labels_dir=r"C:\path\to\labels",  
    output_dir=r"C:\path\to\output",     
    val_ratio=0.2,                      
    img_ext=['.jpg', '.png'],           
    label_ext='.txt',                   
    seed=42                            
)
```

### 参数说明

- `images_dir`: 原始图像文件目录路径
- `labels_dir`: 原始标注文件目录路径 (标注文件应与图像文件同名，后缀不同)
- `output_dir`: 输出数据集的根目录路径
- `val_ratio`: 验证集占比，默认0.2 (即20%验证集，80%训练集)
- `img_ext`: 图像文件扩展名，可以是字符串或列表，默认['.jpg', '.jpeg', '.png', '.bmp']
- `label_ext`: 标注文件扩展名，默认'.txt'
- `seed`: 随机种子，用于确保数据集划分可重现，默认42

### 注意事项

- 标注文件必须与图像文件同名(扩展名不同)
- 在Windows系统中路径请使用r前缀或双反斜杠，如: `r"C:\path"` 或 `"C:\\path"`
- 支持多种图像格式，可以通过img_ext参数指定
- 脚本会自动忽略没有对应标注文件的图像 