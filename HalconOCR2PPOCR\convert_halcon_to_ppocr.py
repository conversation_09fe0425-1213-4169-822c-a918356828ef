#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
将Halcon OCR标注文件转换为ppocr格式
"""
import os
import json
import math
import numpy as np
import random
from pathlib import Path


def rotated_rect_to_points(cx, cy, width, height, angle_rad):
    """
    将旋转矩形转换为四个顶点坐标
    
    参数:
        cx, cy: 矩形中心点坐标
        width, height: 矩形的宽度和高度
        angle_rad: 旋转角度（弧度）
    
    返回:
        四个顶点坐标的列表 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
    """
    # 计算矩形的半宽和半高
    half_width = width / 2
    half_height = height / 2
    
    # 计算旋转矩阵
    cos_angle = math.cos(angle_rad)
    sin_angle = math.sin(angle_rad)
    
    # 计算矩形的四个顶点（顺时针顺序）
    # 左上角
    x1 = cx - half_width * cos_angle - half_height * sin_angle
    y1 = cy - half_width * sin_angle + half_height * cos_angle
    
    # 右上角
    x2 = cx + half_width * cos_angle - half_height * sin_angle
    y2 = cy + half_width * sin_angle + half_height * cos_angle
    
    # 右下角
    x3 = cx + half_width * cos_angle + half_height * sin_angle
    y3 = cy + half_width * sin_angle - half_height * cos_angle
    
    # 左下角
    x4 = cx - half_width * cos_angle + half_height * sin_angle
    y4 = cy - half_width * sin_angle - half_height * cos_angle
    
    return [[int(x1), int(y1)], [int(x2), int(y2)], [int(x3), int(y3)], [int(x4), int(y4)]]


def convert_halcon_to_ppocr(halcon_json_path, output_dir, config=None):
    """
    将Halcon OCR格式转换为ppocr格式
    
    参数:
        halcon_json_path: Halcon OCR JSON文件路径
        output_dir: 输出目录
        config: 配置参数字典
    """
    # 默认配置
    default_config = {
        "train_ratio": 0.8,
        "val_ratio": 0.1,
        "test_ratio": 0.1,
        "random_seed": 42,
        "crop_image": False,
        "crop_dir": "crops"
    }
    
    # 合并配置
    if config is None:
        config = {}
    
    cfg = {**default_config, **config}
    # 创建输出目录
    output_det_dir = os.path.join(output_dir, 'det')
    output_rec_dir = os.path.join(output_dir, 'rec')
    os.makedirs(output_det_dir, exist_ok=True)
    os.makedirs(output_rec_dir, exist_ok=True)
    
    # 创建det目录下的train.txt, val.txt, test.txt
    det_train_path = os.path.join(output_det_dir, 'train.txt')
    det_val_path = os.path.join(output_det_dir, 'val.txt')
    det_test_path = os.path.join(output_det_dir, 'test.txt')
    
    # 创建rec目录下的train.txt, val.txt, test.txt
    rec_train_path = os.path.join(output_rec_dir, 'train.txt')
    rec_val_path = os.path.join(output_rec_dir, 'val.txt')
    rec_test_path = os.path.join(output_rec_dir, 'test.txt')
    
    # 打开文件准备写入
    det_train_file = open(det_train_path, 'w', encoding='utf-8')
    det_val_file = open(det_val_path, 'w', encoding='utf-8')
    det_test_file = open(det_test_path, 'w', encoding='utf-8')
    
    rec_train_file = open(rec_train_path, 'w', encoding='utf-8')
    rec_val_file = open(rec_val_path, 'w', encoding='utf-8')
    rec_test_file = open(rec_test_path, 'w', encoding='utf-8')
    
    # 读取Halcon OCR JSON文件
    with open(halcon_json_path, 'r', encoding='utf-8') as f:
        halcon_data = json.load(f)
    
    # 获取图像目录
    image_dir = halcon_data.get('image_dir', '')
    if not image_dir:
        print("警告: JSON文件中未找到图像目录，使用相对路径.")
    
    # 处理样本
    image_annotations = {}  # 用于按图像分组标注
    
    for sample in halcon_data.get('samples', []):
        image_id = sample.get('image_id')
        image_file_name = sample.get('image_file_name', '')
        
        # 如果图像文件名为空，跳过
        if not image_file_name:
            continue
        
        # 获取标注信息
        text = sample.get('label_custom_data', {}).get('text', '')
        
        # 如果文本为空，跳过
        if not text:
            continue
        
        # 获取边界框信息
        center_row = sample.get('bbox_row', 0)
        center_col = sample.get('bbox_col', 0)
        length1 = sample.get('bbox_length1', 0)  # width
        length2 = sample.get('bbox_length2', 0)  # height
        phi = sample.get('bbox_phi', 0)  # 旋转角度（弧度）
        
        # 将旋转矩形转换为四个顶点坐标
        points = rotated_rect_to_points(center_col, center_row, length1, length2, phi)
        
        # 格式化为ppocr检测格式
        annotation = {
            "transcription": text,
            "points": points,
            "difficult": False
        }
        
        # 按图像分组标注
        if image_file_name not in image_annotations:
            image_annotations[image_file_name] = []
        
        image_annotations[image_file_name].append(annotation)
    
    # 根据图像文件名确定训练、验证和测试集
    image_files = list(image_annotations.keys())
    
    # 设置随机种子以确保结果可重复
    random_seed = cfg["random_seed"]
    random.seed(random_seed)
    np.random.seed(random_seed)
    random.shuffle(image_files)
    
    # 根据配置的比例划分数据集
    n_files = len(image_files)
    train_ratio = cfg["train_ratio"]
    val_ratio = cfg["val_ratio"]
    # test_ratio = cfg["test_ratio"]  # 不需要显式计算
    
    n_train = int(n_files * train_ratio)
    n_val = int(n_files * val_ratio)
    
    train_files = image_files[:n_train]
    val_files = image_files[n_train:n_train+n_val]
    test_files = image_files[n_train+n_val:]
    
    # 生成检测标注文件
    for image_file in train_files:
        full_path = os.path.join(image_dir, image_file)
        annotations = image_annotations[image_file]
        det_train_file.write(f"{full_path}\t{json.dumps(annotations)}\n")
    
    for image_file in val_files:
        full_path = os.path.join(image_dir, image_file)
        annotations = image_annotations[image_file]
        det_val_file.write(f"{full_path}\t{json.dumps(annotations)}\n")
    
    for image_file in test_files:
        full_path = os.path.join(image_dir, image_file)
        annotations = image_annotations[image_file]
        det_test_file.write(f"{full_path}\t{json.dumps(annotations)}\n")
    
    # 生成识别标注文件
    # 在实际应用中，这应该使用裁剪后的文本行图像
    # 这里我们只是使用原始图像和文本作为示例
    crop_id = 0
    for image_file in train_files:
        annotations = image_annotations[image_file]
        for ann in annotations:
            # 实际应用中应该使用裁剪后的图像
            # 这里使用原始图像路径和"_crop_N"作为示例
            image_stem = Path(image_file).stem
            crop_name = f"{image_stem}_crop_{crop_id}.jpg"
            full_path = os.path.join(image_dir, crop_name)
            text = ann["transcription"]
            rec_train_file.write(f"{full_path}\t{text}\n")
            crop_id += 1
    
    crop_id = 0
    for image_file in val_files:
        annotations = image_annotations[image_file]
        for ann in annotations:
            image_stem = Path(image_file).stem
            crop_name = f"{image_stem}_crop_{crop_id}.jpg"
            full_path = os.path.join(image_dir, crop_name)
            text = ann["transcription"]
            rec_val_file.write(f"{full_path}\t{text}\n")
            crop_id += 1
    
    crop_id = 0
    for image_file in test_files:
        annotations = image_annotations[image_file]
        for ann in annotations:
            image_stem = Path(image_file).stem
            crop_name = f"{image_stem}_crop_{crop_id}.jpg"
            full_path = os.path.join(image_dir, crop_name)
            text = ann["transcription"]
            rec_test_file.write(f"{full_path}\t{text}\n")
            crop_id += 1
    
    # 关闭文件
    det_train_file.close()
    det_val_file.close()
    det_test_file.close()
    rec_train_file.close()
    rec_val_file.close()
    rec_test_file.close()
    
    print(f"转换完成! 检测和识别标注文件已生成在 {output_dir} 目录下.")


def main():
    # 命令行参数解析
    import argparse
    parser = argparse.ArgumentParser(description='将Halcon OCR标注文件转换为ppocr格式')
    parser.add_argument('--input', '-i', required=False,default='E:\Python_Demo\MoveFiles\ConvertData\HalconOCR.json', help='Halcon OCR JSON文件路径')
    parser.add_argument('--output', '-o', required=False,default='E:\Python_Demo\MoveFiles\ConvertData', help='输出目录')
    parser.add_argument('--config', '-c', required=False, help='配置文件路径')
    args = parser.parse_args()
    
    # 检查是否有配置文件
    config = {}
    if args.config:
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
    
    # 如果命令行有参数，覆盖配置文件的参数
    if args.input:
        input_path = args.input
    elif 'input' in config:
        input_path = config['input']
    else:
        print('错误: 需要指定输入文件路径')
        return
    
    if args.output:
        output_dir = args.output
    elif 'output' in config:
        output_dir = config['output']
    else:
        print('错误: 需要指定输出目录')
        return
    
    # 执行转换
    convert_halcon_to_ppocr(input_path, output_dir, config)


if __name__ == '__main__':
    main()