"""
完整COCO数据集创建工具
将YOLO格式转换为标准COCO数据集结构，包含train/val分割和对应的标注文件

修改配置参数后直接运行即可生成完整的COCO数据集
"""

from yolo2coco_dataset import YoloToCocoDataset, create_coco_dataset
import os


def main():
    """主函数 - 创建完整COCO数据集"""
    
    # ==================== 配置参数 ====================
    
    # 输入路径配置
    YOLO_IMAGES_DIR = r"E:\datasets\yolo\images"       # YOLO图片文件夹路径
    YOLO_LABELS_DIR = r"E:\datasets\yolo\labels"       # YOLO标注文件夹路径
    
    # 输出路径配置
    OUTPUT_DIR = r"E:\datasets\coco_dataset"           # 输出COCO数据集根目录
    
    # 类别配置（按照YOLO标注文件中的类别ID顺序）
    CLASS_NAMES = [
        "person",       # 类别ID: 0
        "car",          # 类别ID: 1
        "bicycle",      # 类别ID: 2
        "motorcycle",   # 类别ID: 3
        "bus",          # 类别ID: 4
        "truck",        # 类别ID: 5
        # 根据您的数据集添加更多类别...
    ]
    
    # 数据集分割比例配置
    TRAIN_RATIO = 0.8   # 训练集比例 (80%)
    VAL_RATIO = 0.2     # 验证集比例 (20%)
    TEST_RATIO = 0.0    # 测试集比例 (0%, 可选)
    
    # 任务类型配置
    TASK_TYPE = "detection"  # "detection" 或 "segmentation"
    
    # 随机种子（用于可重复的数据集分割）
    RANDOM_SEED = 42
    
    # ==================== 执行转换 ====================
    
    print("=" * 70)
    print("YOLO到COCO完整数据集转换工具")
    print("=" * 70)
    
    # 显示配置信息
    print("配置信息:")
    print(f"  YOLO图片目录: {YOLO_IMAGES_DIR}")
    print(f"  YOLO标注目录: {YOLO_LABELS_DIR}")
    print(f"  输出COCO目录: {OUTPUT_DIR}")
    print(f"  类别数量: {len(CLASS_NAMES)}")
    print(f"  类别列表: {CLASS_NAMES}")
    print(f"  任务类型: {TASK_TYPE}")
    print(f"  训练集比例: {TRAIN_RATIO}")
    print(f"  验证集比例: {VAL_RATIO}")
    print(f"  测试集比例: {TEST_RATIO}")
    print(f"  随机种子: {RANDOM_SEED}")
    print("-" * 70)
    
    # 验证输入路径
    if not os.path.exists(YOLO_IMAGES_DIR):
        print(f"❌ 错误：YOLO图片文件夹不存在: {YOLO_IMAGES_DIR}")
        return
    
    if not os.path.exists(YOLO_LABELS_DIR):
        print(f"❌ 错误：YOLO标注文件夹不存在: {YOLO_LABELS_DIR}")
        return
    
    # 检查输出目录
    if os.path.exists(OUTPUT_DIR):
        response = input(f"⚠️  输出目录已存在: {OUTPUT_DIR}\\n是否继续？(y/n): ")
        if response.lower() != 'y':
            print("转换已取消")
            return
    
    try:
        # 创建转换器并执行转换
        converter = YoloToCocoDataset(
            yolo_images_dir=YOLO_IMAGES_DIR,
            yolo_labels_dir=YOLO_LABELS_DIR,
            class_names=CLASS_NAMES,
            output_dir=OUTPUT_DIR,
            train_ratio=TRAIN_RATIO,
            val_ratio=VAL_RATIO,
            test_ratio=TEST_RATIO
        )
        
        # 执行转换
        results = converter.convert(task_type=TASK_TYPE, random_seed=RANDOM_SEED)
        
        # 显示转换结果
        print("\\n✅ 转换成功完成！")
        print("\\n📊 转换统计:")
        total_images = 0
        total_annotations = 0
        
        for split_name, coco_data in results.items():
            images_count = len(coco_data['images'])
            annotations_count = len(coco_data['annotations'])
            total_images += images_count
            total_annotations += annotations_count
            print(f"  {split_name:>5} 集: {images_count:>4} 张图片, {annotations_count:>5} 个标注")
        
        print(f"  {'总计':>5}: {total_images:>4} 张图片, {total_annotations:>5} 个标注")
        
        # 显示生成的文件结构
        print("\\n📁 生成的COCO数据集结构:")
        print(f"{OUTPUT_DIR}/")
        print("├── images/")
        for split_name in results.keys():
            print(f"│   ├── {split_name}/")
        print("├── annotations/")
        for split_name in results.keys():
            print(f"│   ├── {split_name}.json")
        print("└── dataset_info.json")
        
        print(f"\\n🎉 COCO数据集已成功创建在: {OUTPUT_DIR}")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()


def quick_detection_dataset():
    """快速创建检测数据集示例"""
    try:
        results = create_coco_dataset(
            yolo_images_dir=r"E:\my_yolo_dataset\images",
            yolo_labels_dir=r"E:\my_yolo_dataset\labels",
            class_names=["person", "car"],
            output_dir=r"E:\my_coco_dataset",
            task_type="detection",
            train_ratio=0.8,
            val_ratio=0.2
        )
        print("快速检测数据集创建完成！")
        return results
    except Exception as e:
        print(f"快速创建失败: {e}")


def quick_segmentation_dataset():
    """快速创建分割数据集示例"""
    try:
        results = create_coco_dataset(
            yolo_images_dir=r"E:\my_yolo_dataset\images",
            yolo_labels_dir=r"E:\my_yolo_dataset\labels",
            class_names=["person", "car"],
            output_dir=r"E:\my_coco_seg_dataset",
            task_type="segmentation",
            train_ratio=0.7,
            val_ratio=0.2,
            test_ratio=0.1
        )
        print("快速分割数据集创建完成！")
        return results
    except Exception as e:
        print(f"快速创建失败: {e}")


def validate_coco_dataset(coco_dir):
    """验证生成的COCO数据集结构"""
    print(f"验证COCO数据集: {coco_dir}")
    
    required_dirs = ['images', 'annotations']
    for dir_name in required_dirs:
        dir_path = os.path.join(coco_dir, dir_name)
        if os.path.exists(dir_path):
            print(f"✅ {dir_name}/ 目录存在")
        else:
            print(f"❌ {dir_name}/ 目录缺失")
    
    # 检查标注文件
    annotations_dir = os.path.join(coco_dir, 'annotations')
    if os.path.exists(annotations_dir):
        json_files = [f for f in os.listdir(annotations_dir) if f.endswith('.json')]
        print(f"📄 找到 {len(json_files)} 个标注文件: {json_files}")
    
    # 检查数据集信息文件
    info_file = os.path.join(coco_dir, 'dataset_info.json')
    if os.path.exists(info_file):
        print("✅ dataset_info.json 存在")
        try:
            import json
            with open(info_file, 'r', encoding='utf-8') as f:
                info = json.load(f)
            print(f"📊 数据集信息: {info['total_images']} 张图片, {len(info['classes']['names'])} 个类别")
        except Exception as e:
            print(f"❌ 读取数据集信息失败: {e}")
    else:
        print("❌ dataset_info.json 缺失")


if __name__ == "__main__":
    # 运行主转换程序
    main()
    
    # 可选：验证生成的数据集
    # validate_coco_dataset(r"E:\datasets\coco_dataset")
    
    # 或者运行快速示例
    # quick_detection_dataset()
    # quick_segmentation_dataset()