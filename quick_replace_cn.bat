@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ================================================
echo           DLL文件替换工具快速启动
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查dll_replacer.py是否存在
if not exist "dll_replacer.py" (
    echo 错误: dll_replacer.py 文件不存在
    pause
    exit /b 1
)

REM 提示用户输入新DLL路径
set /p NEW_DLL="请输入新DLL文件的完整路径: "
if "%NEW_DLL%"=="" (
    echo 错误: 新DLL路径不能为空
    pause
    exit /b 1
)

REM 检查新DLL文件是否存在
if not exist "%NEW_DLL%" (
    echo 错误: 指定的新DLL文件不存在: %NEW_DLL%
    pause
    exit /b 1
)

echo.
echo 新DLL文件: %NEW_DLL%
echo.

REM 询问操作模式
echo 请选择操作模式:
echo 1. 预览模式 (推荐，仅查看将要替换的文件)
echo 2. 实际替换模式
echo.
set /p MODE="请输入选择 (1 或 2): "

if "%MODE%"=="1" (
    echo.
    echo 正在启动预览模式...
    python dll_replacer.py --new-dll "%NEW_DLL%" --dry-run --verbose
) else (
    if "%MODE%"=="2" (
        echo.
        echo 警告: 即将执行实际替换操作！
        set /p CONFIRM="确定要继续吗? (y/N): "
        if /i "%CONFIRM%"=="y" (
            echo.
            echo 正在执行DLL替换...
            python dll_replacer.py --new-dll "%NEW_DLL%" --verbose
        ) else (
            echo 操作已取消
        )
    ) else (
        echo 无效选择
    )
)

echo.
echo 操作完成，按任意键退出...
pause >nul