# -*- coding: utf-8 -*-
import os
import cv2
import numpy as np

def read_yolo_labels(label_path, original_size, crop_info):
    # """
    # 获取YOLO格式的标签文件的信息
    # original_size: (原始宽度, 原始高度)
    # crop_info: (y_start, x_start, width, height) 裁剪信息
    # """
    boxes = []
    if os.path.exists(label_path):
        orig_w, orig_h = original_size
        y_start, x_start, crop_width, crop_height = crop_info
        
        with open(label_path, 'r') as f:
            for line in f.readlines():
                class_id, x, y, w, h = map(float, line.strip().split())
                
                # 将YOLO格式转换为原始图上的坐标
                x_center_px = int(x * orig_w)
                y_center_px = int(y * orig_h)
                width_px = int(w * orig_w)
                height_px = int(h * orig_h)
                
                # 计算边界框在原始图中的位置
                x1 = x_center_px - width_px//2  # 左上角
                y1 = y_center_px - height_px//2  # 上左角
                x2 = x1 + width_px              # 右下角
                y2 = y1 + height_px             # 下右角
                
                # 转换到裁剪图中的位置
                x1_new = x1 - x_start
                y1_new = y1 - y_start
                x2_new = x2 - x_start
                y2_new = y2 - y_start
                
                # 判断是否在裁剪图中
                if (x2_new > 0 and x1_new < crop_width and 
                    y2_new > 0 and y1_new < crop_height):
                    # 裁剪图中的坐标
                    x1_new = max(0, x1_new)
                    y1_new = max(0, y1_new)
                    x2_new = min(crop_width, x2_new)
                    y2_new = min(crop_height, y2_new)
                    
                    # 计算裁剪图中的边界框的宽度和高度
                    new_width = x2_new - x1_new
                    new_height = y2_new - y1_new
                    new_x_center = x1_new + new_width/2
                    new_y_center = y1_new + new_height/2
                    
                    # 转换为YOLO格式
                    new_x = new_x_center / crop_width
                    new_y = new_y_center / crop_height
                    new_w = new_width / crop_width
                    new_h = new_height / crop_height
                    
                    boxes.append([class_id, new_x, new_y, new_w, new_h])
    return boxes

def save_yolo_labels(boxes, output_path):
    # """转换为YOLO格式标签"""
    with open(output_path, 'w') as f:
        for box in boxes:
            class_id, x, y, w, h = box
            # 确保class_id是整数
            line = f"{int(class_id)} {x:.6f} {y:.6f} {w:.6f} {h:.6f}\n"
            f.write(line)

def draw_boxes(image, boxes, class_names=None):
    # """在图像上绘制边界框"""
    height, width = image.shape[:2]
    for box in boxes:
        class_id, x, y, w, h = box
        
        # 将YOLO格式转换为OpenCV格式
        x_center = int(x * width)
        y_center = int(y * height)
        box_width = int(w * width)
        box_height = int(h * height)
        
        # 计算边界框的四个角点
        x1 = int(x_center - box_width/2)
        y1 = int(y_center - box_height/2)
        x2 = int(x_center + box_width/2)
        y2 = int(y_center + box_height/2)
        
        # 确保角点在图像边界内
        x1 = max(0, x1)
        y1 = max(0, y1)
        x2 = min(width, x2)
        y2 = min(height, y2)
        
        # 绘制边界框
        color = (0, 255, 0)  # 绿色
        cv2.rectangle(image, (x1, y1), (x2, y2), color, 2)
        
        # 提供class_names, 用于显示
        if class_names is not None and int(class_id) < len(class_names):
            label = class_names[int(class_id)]
            cv2.putText(image, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    return image

def process_images_and_labels(image_dir, output_base_dir, class_names=None, only_crop=False):
    """
    处理图像及其对应的标签
    image_dir: 原始图目录
    output_base_dir: 输出目录
    class_names: 类别列表
    only_crop: 是否只裁剪图像，不生成标签
    """
    # 获取数据集名称
    dataset_name = os.path.basename(image_dir)  # 获取目录名
    
    # 标签目录路径，与原始图目录同名
    label_dir = image_dir.replace('images', 'labels')  # 标签目录
    
    # 创建输出目录
    if only_crop:
        # 如果只裁剪图像，则直接使用输出目录
        crop_dir = output_base_dir  # 直接使用输出目录
    else:
        # 如果需要生成标签，则需要创建裁剪图像和标签目录
        crop_dir = os.path.join(output_base_dir, 'crop')  # 裁剪图像目录
        label_output_dir = os.path.join(output_base_dir, 'labels')  # 转换后的标签目录
        visual_dir = os.path.join(output_base_dir, 'visual')  # 可视化目录
        os.makedirs(label_output_dir, exist_ok=True)
        os.makedirs(visual_dir, exist_ok=True)
    
    # 创建裁剪图像目录
    os.makedirs(crop_dir, exist_ok=True)
    
    # Halcon裁剪信息 (row1, col1, row2, col2) 
    row1=300
    col1=15
    row2=400+1
    col2=470+1 
    # row1=104
    # col1=14
    # row2=585+1 
    # col2=528+1
    halcon_crop = (row1, col1, row2, col2)
    
    print(f'处理目录: {image_dir}')
    if not only_crop:
        print(f'标签目录: {label_dir}')
    print(f'裁剪图像目录: {crop_dir}')
    if not only_crop:
        print(f'标签输出目录: {label_output_dir}')
        print(f'可视化目录: {visual_dir}')
    
    for image_name in os.listdir(image_dir):
        if image_name.endswith(('.jpg', '.png', '.jpeg', '.bmp')):
            # 获取图像路径
            image_path = os.path.join(image_dir, image_name)
            crop_image_path = os.path.join(crop_dir, image_name)  # 裁剪图像路径
            
            # 读取图像并获取原始图像尺寸
            image = cv2.imread(image_path)
            if image is None:
                print(f"无法读取图像: {image_path}")
                continue
            
            # 裁剪图像 - OpenCV使用[y, x]顺序
            y1, x1, y2, x2 = halcon_crop
            cropped_image = image[int(y1):int(y2), int(x1):int(x2)]  # [y1:y2, x1:x2]
            
            # 保存裁剪图像
            cv2.imwrite(crop_image_path, cropped_image)
            print(f'保存裁剪图像: {crop_image_path}')
            
            if not only_crop:
                # 如果需要生成标签，则执行标签转换和可视化
                original_size = (image.shape[1], image.shape[0])  # 获取原始图像尺寸 (宽度, 高度)
                crop_height = int(y2 - y1)
                crop_width = int(x2 - x1)
                crop_info = (int(y1), int(x1), crop_width, crop_height)
                
                # 获取标签路径
                label_path = os.path.join(label_dir, os.path.splitext(image_name)[0] + '.txt')
                output_label_path = os.path.join(label_output_dir, os.path.splitext(image_name)[0] + '.txt')
                visual_path = os.path.join(visual_dir, f'visual_{image_name}')
                
                # 获取转换后的标签
                boxes = read_yolo_labels(label_path, original_size, crop_info)
                
                # 保存转换后的标签
                save_yolo_labels(boxes, output_label_path)
                print(f'保存转换后的标签: {output_label_path}')
                
                # 绘制边界框并保存可视化图像
                if boxes:
                    visualized_image = draw_boxes(cropped_image.copy(), boxes, class_names)
                    cv2.imwrite(visual_path, visualized_image)
                    print(f'保存可视化图像: {visual_path} (包含 {len(boxes)} 个标注)')
                else:
                    print(f'警告: {image_name} 没有标注在裁剪图像中')

if __name__ == '__main__':
    # 输入原始图目录
    image_dir = r'D:\WH_WorkFiles\ProjectFile\03RPA\DataSets\Datasets20250219\images\val'
    
    # 创建输出目录
    output_base_dir = r'D:\Desktop\crop\outputval'
    
    # 选择类别列表
    class_names = ['kerf']
    
    # 是否只裁剪图像
    only_crop =False
    
    # 处理图像及其对应的标签
    process_images_and_labels(image_dir, output_base_dir, class_names, only_crop)
