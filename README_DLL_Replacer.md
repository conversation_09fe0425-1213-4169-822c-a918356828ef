# DLL文件替换工具

一个功能强大的DLL文件替换工具，支持批量搜索和替换系统中的指定DLL文件。

## 功能特性

- 🔍 **智能搜索**：自动搜索系统中所有匹配的DLL文件
- 📂 **参数化配置**：灵活配置新旧DLL路径和搜索范围
- 🛡️ **安全备份**：自动备份原文件，支持完整的恢复机制
- 📋 **版本检查**：比较文件版本，避免不必要的替换
- 🔒 **权限处理**：检测管理员权限，处理文件占用情况
- 📊 **详细日志**：完整的操作日志和统计报告
- 👁️ **预览模式**：支持干运行，预览操作而不实际执行

## 安装依赖

```bash
pip install -r requirements.txt
```

## 基本用法

### 1. 简单替换
```bash
python dll_replacer.py --new-dll "C:\path\to\new\halcondotnet.dll"
```

### 2. 指定搜索路径
```bash
python dll_replacer.py --new-dll "new.dll" --search-paths "C:\Program Files" "D:\MyApp"
```

### 3. 预览模式（推荐）
```bash
python dll_replacer.py --new-dll "new.dll" --dry-run
```

### 4. 强制替换
```bash
python dll_replacer.py --new-dll "new.dll" --force --yes
```

## 参数说明

### 必需参数
- `--new-dll, -n`: 新DLL文件的完整路径

### 可选参数
- `--target-name, -t`: 目标DLL文件名 (默认: halcondotnet.dll)
- `--search-paths, -s`: 搜索路径列表 (默认: 系统和程序目录)
- `--exclude-paths, -e`: 排除的路径列表
- `--backup-dir, -b`: 备份目录 (默认: ./dll_backup)
- `--dry-run, -d`: 预览模式，不实际执行替换
- `--force, -f`: 强制替换，忽略版本检查和文件占用
- `--yes, -y`: 自动确认所有操作
- `--no-backup`: 不创建备份文件
- `--verbose, -v`: 显示详细日志
- `--log-dir`: 日志目录 (默认: ./logs)
- `--report-dir`: 报告目录 (默认: ./reports)

## 使用场景示例

### 场景1: 更新软件的DLL组件
```bash
# 预览将要替换的文件
python dll_replacer.py --new-dll "C:\Updates\halcondotnet.dll" --dry-run

# 确认无误后执行替换
python dll_replacer.py --new-dll "C:\Updates\halcondotnet.dll"
```

### 场景2: 替换特定目录下的DLL
```bash
python dll_replacer.py \
  --new-dll "new_halcon.dll" \
  --search-paths "C:\MyApp" "D:\Software" \
  --backup-dir "C:\Backups\DLL"
```

### 场景3: 批量替换不同名称的DLL
```bash
python dll_replacer.py --new-dll "new.dll" --target-name "old_version.dll"
```

### 场景4: 安全的系统级替换
```bash
# 以管理员身份运行
python dll_replacer.py \
  --new-dll "system_update.dll" \
  --search-paths "C:\Windows\System32" \
  --force \
  --verbose
```

## 安全注意事项

1. **管理员权限**：替换系统文件需要管理员权限
2. **备份重要**：默认会创建备份，建议不要使用 `--no-backup`
3. **预览优先**：首次使用建议先用 `--dry-run` 预览
4. **版本检查**：工具会自动检查版本，避免降级
5. **文件占用**：会检测文件是否被进程占用

## 输出文件结构

```
项目目录/
├── dll_backup/          # 备份文件目录
│   ├── backup_index_*.json  # 备份索引文件
│   └── [原始目录结构]   # 保持原始路径的备份文件
├── logs/                # 日志文件目录
│   └── dll_replacer_*.log   # 操作日志
└── reports/             # 报告文件目录
    └── dll_replacer_report_*.json  # 操作报告
```

## 故障排除

### 问题1: 权限不足
**解决方案**: 以管理员身份运行命令提示符

### 问题2: 文件被占用
**解决方案**: 
- 关闭相关应用程序
- 使用 `--force` 参数强制替换

### 问题3: 找不到目标文件
**解决方案**:
- 检查 `--target-name` 参数是否正确
- 扩大 `--search-paths` 范围
- 使用 `--verbose` 查看详细搜索过程

### 问题4: 版本回退警告
**解决方案**:
- 确认新DLL版本是否正确
- 使用 `--force` 强制替换

## 恢复备份

如果需要恢复备份的文件：

1. 查看备份索引文件 `backup_index_*.json`
2. 手动将备份文件复制回原位置
3. 或编写恢复脚本根据索引批量恢复

## 技术特性

- **跨驱动器搜索**：支持搜索多个驱动器
- **文件完整性**：使用MD5哈希验证文件完整性
- **进程检测**：检测使用DLL的进程
- **版本比较**：读取PE文件版本信息
- **原子操作**：确保替换过程的原子性

## 开发信息

- **语言**: Python 3.6+
- **平台**: Windows
- **依赖**: pywin32, psutil
- **许可**: MIT License 