import json
import os
import cv2
from datetime import datetime


class YoloToCoco:
    """YOLO到COCO格式转换器"""
    
    def __init__(self, images_dir, labels_dir, class_names, output_path):
        """
        初始化转换器
        
        :param images_dir: 图片文件夹路径
        :param labels_dir: YOLO标注文件夹路径
        :param class_names: 类别名称列表
        :param output_path: 输出COCO JSON文件路径
        """
        self.images_dir = images_dir
        self.labels_dir = labels_dir
        self.class_names = class_names
        self.output_path = output_path
        
        # 验证路径
        if not os.path.exists(images_dir):
            raise ValueError(f"图片文件夹不存在: {images_dir}")
        if not os.path.exists(labels_dir):
            raise ValueError(f"标注文件夹不存在: {labels_dir}")
        
        # 创建输出目录
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def _create_coco_structure(self):
        """创建COCO数据结构"""
        coco_data = {
            "info": {
                "description": "YOLO to COCO conversion",
                "version": "1.0",
                "year": datetime.now().year,
                "contributor": "Auto-generated",
                "date_created": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            },
            "licenses": [{"id": 1, "name": "Unknown", "url": ""}],
            "images": [],
            "annotations": [],
            "categories": []
        }
        
        # 添加类别
        for i, class_name in enumerate(self.class_names):
            coco_data["categories"].append({
                "id": i,
                "name": class_name,
                "supercategory": "object"
            })
        
        return coco_data
    
    def _get_image_files(self):
        """获取图片文件列表"""
        image_files = []
        supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        
        for file in os.listdir(self.images_dir):
            if any(file.lower().endswith(ext) for ext in supported_formats):
                image_files.append(file)
        
        return sorted(image_files)
    
    def convert_detection(self):
        """转换检测任务"""
        print("开始转换YOLO检测格式到COCO格式...")
        
        coco_data = self._create_coco_structure()
        annotation_id = 0
        
        image_files = self._get_image_files()
        print(f"找到 {len(image_files)} 张图片")
        
        for image_id, image_file in enumerate(image_files):
            image_path = os.path.join(self.images_dir, image_file)
            
            # 获取图片尺寸
            img = cv2.imread(image_path)
            if img is None:
                print(f"警告：无法读取图片 {image_file}")
                continue
            
            height, width = img.shape[:2]
            
            # 添加图片信息
            coco_data["images"].append({
                "id": image_id,
                "width": width,
                "height": height,
                "file_name": image_file,
                "license": 1,
                "date_captured": ""
            })
            
            # 处理对应的标注文件
            label_file = os.path.splitext(image_file)[0] + '.txt'
            label_path = os.path.join(self.labels_dir, label_file)
            
            if os.path.exists(label_path):
                with open(label_path, 'r') as f:
                    for line_num, line in enumerate(f, 1):
                        line = line.strip()
                        if not line:
                            continue
                        
                        parts = line.split()
                        if len(parts) < 5:
                            print(f"警告：{label_file} 第{line_num}行格式错误")
                            continue
                        
                        try:
                            class_id = int(parts[0])
                            cx, cy, w, h = map(float, parts[1:5])
                            
                            # 验证类别ID
                            if class_id >= len(self.class_names):
                                print(f"警告：{label_file} 第{line_num}行类别ID {class_id} 超出范围")
                                continue
                            
                            # YOLO格式转COCO格式
                            x = (cx - w/2) * width
                            y = (cy - h/2) * height
                            bbox_width = w * width
                            bbox_height = h * height
                            
                            # 边界检查
                            x = max(0, x)
                            y = max(0, y)
                            bbox_width = min(bbox_width, width - x)
                            bbox_height = min(bbox_height, height - y)
                            
                            if bbox_width <= 0 or bbox_height <= 0:
                                print(f"警告：{label_file} 第{line_num}行边界框无效")
                                continue
                            
                            coco_data["annotations"].append({
                                "id": annotation_id,
                                "image_id": image_id,
                                "category_id": class_id,
                                "bbox": [x, y, bbox_width, bbox_height],
                                "area": bbox_width * bbox_height,
                                "segmentation": [],
                                "iscrowd": 0
                            })
                            annotation_id += 1
                            
                        except ValueError as e:
                            print(f"警告：{label_file} 第{line_num}行数据格式错误: {e}")
                            continue
            else:
                print(f"警告：找不到对应的标注文件 {label_file}")
        
        # 保存文件
        with open(self.output_path, 'w', encoding='utf-8') as f:
            json.dump(coco_data, f, indent=2, ensure_ascii=False)
        
        print(f"检测任务转换完成！")
        print(f"图片数量: {len(coco_data['images'])}")
        print(f"标注数量: {len(coco_data['annotations'])}")
        print(f"输出文件: {self.output_path}")
        
        return coco_data
    
    def convert_segmentation(self):
        """转换分割任务"""
        print("开始转换YOLO分割格式到COCO格式...")
        
        coco_data = self._create_coco_structure()
        annotation_id = 0
        
        image_files = self._get_image_files()
        print(f"找到 {len(image_files)} 张图片")
        
        for image_id, image_file in enumerate(image_files):
            image_path = os.path.join(self.images_dir, image_file)
            
            # 获取图片尺寸
            img = cv2.imread(image_path)
            if img is None:
                print(f"警告：无法读取图片 {image_file}")
                continue
            
            height, width = img.shape[:2]
            
            # 添加图片信息
            coco_data["images"].append({
                "id": image_id,
                "width": width,
                "height": height,
                "file_name": image_file,
                "license": 1,
                "date_captured": ""
            })
            
            # 处理对应的标注文件
            label_file = os.path.splitext(image_file)[0] + '.txt'
            label_path = os.path.join(self.labels_dir, label_file)
            
            if os.path.exists(label_path):
                with open(label_path, 'r') as f:
                    for line_num, line in enumerate(f, 1):
                        line = line.strip()
                        if not line:
                            continue
                        
                        parts = line.split()
                        if len(parts) < 7:  # 至少需要类别 + 3个点
                            print(f"警告：{label_file} 第{line_num}行格式错误（点数不足）")
                            continue
                        
                        try:
                            class_id = int(parts[0])
                            coords = list(map(float, parts[1:]))
                            
                            # 验证类别ID
                            if class_id >= len(self.class_names):
                                print(f"警告：{label_file} 第{line_num}行类别ID {class_id} 超出范围")
                                continue
                            
                            if len(coords) % 2 != 0:
                                print(f"警告：{label_file} 第{line_num}行坐标数量不是偶数")
                                continue
                            
                            # 转换为像素坐标
                            segmentation = []
                            for i in range(0, len(coords), 2):
                                x = coords[i] * width
                                y = coords[i+1] * height
                                segmentation.extend([x, y])
                            
                            # 计算边界框
                            x_coords = segmentation[::2]
                            y_coords = segmentation[1::2]
                            
                            x_min, x_max = min(x_coords), max(x_coords)
                            y_min, y_max = min(y_coords), max(y_coords)
                            
                            bbox_width = x_max - x_min
                            bbox_height = y_max - y_min
                            
                            if bbox_width <= 0 or bbox_height <= 0:
                                print(f"警告：{label_file} 第{line_num}行分割区域无效")
                                continue
                            
                            coco_data["annotations"].append({
                                "id": annotation_id,
                                "image_id": image_id,
                                "category_id": class_id,
                                "bbox": [x_min, y_min, bbox_width, bbox_height],
                                "area": bbox_width * bbox_height,
                                "segmentation": [segmentation],
                                "iscrowd": 0
                            })
                            annotation_id += 1
                            
                        except ValueError as e:
                            print(f"警告：{label_file} 第{line_num}行数据格式错误: {e}")
                            continue
            else:
                print(f"警告：找不到对应的标注文件 {label_file}")
        
        # 保存文件
        with open(self.output_path, 'w', encoding='utf-8') as f:
            json.dump(coco_data, f, indent=2, ensure_ascii=False)
        
        print(f"分割任务转换完成！")
        print(f"图片数量: {len(coco_data['images'])}")
        print(f"标注数量: {len(coco_data['annotations'])}")
        print(f"输出文件: {self.output_path}")
        
        return coco_data


# 便捷函数
def yolo_to_coco_detection(images_dir, labels_dir, class_names, output_path):
    """
    YOLO检测格式转COCO格式的便捷函数
    
    :param images_dir: 图片文件夹路径
    :param labels_dir: YOLO标注文件夹路径
    :param class_names: 类别名称列表
    :param output_path: 输出COCO JSON文件路径
    :return: COCO数据字典
    """
    converter = YoloToCoco(images_dir, labels_dir, class_names, output_path)
    return converter.convert_detection()


def yolo_to_coco_segmentation(images_dir, labels_dir, class_names, output_path):
    """
    YOLO分割格式转COCO格式的便捷函数
    
    :param images_dir: 图片文件夹路径
    :param labels_dir: YOLO标注文件夹路径
    :param class_names: 类别名称列表
    :param output_path: 输出COCO JSON文件路径
    :return: COCO数据字典
    """
    converter = YoloToCoco(images_dir, labels_dir, class_names, output_path)
    return converter.convert_segmentation()


# 使用示例
if __name__ == "__main__":
    # 配置参数
    IMAGES_DIR = r"E:\datasets\images"          # 图片文件夹路径
    LABELS_DIR = r"E:\datasets\labels"          # YOLO标注文件夹路径
    CLASS_NAMES = ["person", "car", "bicycle"]  # 类别名称列表
    OUTPUT_PATH = r"E:\datasets\annotations.json"  # 输出COCO文件路径
    
    # 方法1: 使用类
    try:
        converter = YoloToCoco(IMAGES_DIR, LABELS_DIR, CLASS_NAMES, OUTPUT_PATH)
        
        # 转换检测任务
        # coco_data = converter.convert_detection()
        
        # 转换分割任务
        # coco_data = converter.convert_segmentation()
        
    except Exception as e:
        print(f"转换失败: {e}")
    
    # 方法2: 使用便捷函数
    try:
        # 检测任务转换
        # coco_data = yolo_to_coco_detection(IMAGES_DIR, LABELS_DIR, CLASS_NAMES, OUTPUT_PATH)
        
        # 分割任务转换
        # coco_data = yolo_to_coco_segmentation(IMAGES_DIR, LABELS_DIR, CLASS_NAMES, OUTPUT_PATH)
        
        pass
    except Exception as e:
        print(f"转换失败: {e}")
    
    print("请修改上面的路径配置后取消注释相应的转换代码")