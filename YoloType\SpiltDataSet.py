#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SpiltDataSet.py - 分割YOLO格式数据集为训练集、验证集和测试集

这个脚本用于将YOLO格式数据集（图像和对应的标签文件）随机分割为训练集、
验证集和测试集，并将分割后的文件复制到对应的目录中。

主要功能：
1. 从源目录读取所有图像文件及其对应的标签文件
2. 按照指定的比例随机分割为训练集、验证集和测试集
3. 创建对应的目录结构（images/train, images/val, images/test等）
4. 将分割后的文件复制到对应的目录中

适用于准备机器学习/深度学习训练数据，特别是YOLO目标检测数据集的场景。
"""
import os
import random
import shutil
from sklearn.model_selection import train_test_split

def split_yolo_dataset(dataset_path, output_path, train_ratio=0.7, val_ratio=0.1, test_ratio=0.2):
    # Ensure ratios sum to 1
    assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-5, "Ratios must sum to 1"

    # Get all image files with corresponding label files
    image_files = []
    for f in os.listdir(dataset_path):
        if f.endswith(('.jpg', '.jpeg', '.png')):
            label_file = os.path.splitext(f)[0] + '.txt'
            if os.path.exists(os.path.join(dataset_path, label_file)):
                image_files.append(f)
            else:
                print(f"Skipping {f} due to missing label file")

    # Split dataset
    train_val, test = train_test_split(image_files, test_size=test_ratio, random_state=42)
    train, val = train_test_split(train_val, test_size=val_ratio/(train_ratio+val_ratio), random_state=42)

    # Create output directories
    for subset in ['train', 'val', 'test']:
        os.makedirs(os.path.join(output_path, 'images', subset), exist_ok=True)
        os.makedirs(os.path.join(output_path, 'labels', subset), exist_ok=True)

    # Copy files to appropriate directories
    for subset, files in zip(['train', 'val', 'test'], [train, val, test]):
        for f in files:
            # Copy image
            shutil.copy(os.path.join(dataset_path, f), 
                        os.path.join(output_path, 'images', subset, f))
            
            # Copy corresponding label file
            label_file = os.path.splitext(f)[0] + '.txt'
            shutil.copy(os.path.join(dataset_path, label_file), 
                        os.path.join(output_path, 'labels', subset, label_file))

    print(f"Dataset split completed. Train: {len(train)}, Validation: {len(val)}, Test: {len(test)}")

# 使用示例
dataset_path = r'D:\WH_WorkFiles\ProjectFile\03RPA\DataSet\data'
output_path = r'D:\WH_WorkFiles\ProjectFile\03RPA\DataSet\output'
split_yolo_dataset(dataset_path, output_path)