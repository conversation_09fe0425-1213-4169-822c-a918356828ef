"""
简化版COCO数据集可视化工具

快速查看和验证COCO格式标注的简单工具
修改配置参数后直接运行即可
"""

import json
import cv2
import numpy as np
import os
import random
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Polygon


def load_coco_data(annotation_file):
    """加载COCO数据"""
    with open(annotation_file, 'r', encoding='utf-8') as f:
        return json.load(f)


def generate_colors(num_categories):
    """生成随机颜色"""
    colors = []
    for i in range(num_categories):
        color = (
            random.randint(50, 255),
            random.randint(50, 255),
            random.randint(50, 255)
        )
        colors.append(color)
    return colors


def show_dataset_info(coco_data):
    """显示数据集基本信息"""
    print("\\n" + "="*60)
    print("📊 COCO数据集信息")
    print("="*60)
    
    print(f"📁 基本统计:")
    print(f"   图片数量: {len(coco_data['images'])}")
    print(f"   标注数量: {len(coco_data['annotations'])}")
    print(f"   类别数量: {len(coco_data['categories'])}")
    
    print(f"\\n🏷️  类别列表:")
    for i, category in enumerate(coco_data['categories']):
        print(f"   {i+1:>2}. {category['name']} (ID: {category['id']})")
    
    # 统计每个类别的标注数量
    category_counts = {}
    for ann in coco_data['annotations']:
        cat_id = ann['category_id']
        category_counts[cat_id] = category_counts.get(cat_id, 0) + 1
    
    print(f"\\n📈 类别分布:")
    for category in coco_data['categories']:
        cat_id = category['id']
        count = category_counts.get(cat_id, 0)
        percentage = count / len(coco_data['annotations']) * 100 if coco_data['annotations'] else 0
        print(f"   {category['name']:>15}: {count:>5} 个标注 ({percentage:>5.1f}%)")
    
    print("="*60)


def visualize_image(coco_data, images_dir, image_id=None, image_filename=None, 
                   show_bbox=True, show_segmentation=True, show_labels=True):
    """可视化单张图片"""
    
    # 创建索引
    image_id_to_info = {img['id']: img for img in coco_data['images']}
    category_id_to_info = {cat['id']: cat for cat in coco_data['categories']}
    
    # 按图片ID分组标注
    image_id_to_annotations = {}
    for ann in coco_data['annotations']:
        img_id = ann['image_id']
        if img_id not in image_id_to_annotations:
            image_id_to_annotations[img_id] = []
        image_id_to_annotations[img_id].append(ann)
    
    # 获取图片信息
    if image_id is not None:
        if image_id not in image_id_to_info:
            print(f"❌ 图片ID {image_id} 不存在")
            return
        image_info = image_id_to_info[image_id]
    elif image_filename is not None:
        image_info = None
        for img in coco_data['images']:
            if img['file_name'] == image_filename:
                image_info = img
                image_id = img['id']
                break
        if image_info is None:
            print(f"❌ 图片文件 {image_filename} 不存在")
            return
    else:
        # 随机选择一张图片
        image_info = random.choice(coco_data['images'])
        image_id = image_info['id']
    
    # 加载图片
    image_path = os.path.join(images_dir, image_info['file_name'])
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 获取该图片的所有标注
    annotations = image_id_to_annotations.get(image_id, [])
    
    print(f"\\n🖼️  显示图片: {image_info['file_name']}")
    print(f"   图片尺寸: {image_info['width']} x {image_info['height']}")
    print(f"   标注数量: {len(annotations)}")
    
    # 生成颜色
    colors = generate_colors(len(coco_data['categories']))
    category_colors = {cat['id']: colors[i] for i, cat in enumerate(coco_data['categories'])}
    
    # 创建matplotlib图形
    fig, ax = plt.subplots(1, 1, figsize=(15, 10))
    ax.imshow(image)
    ax.set_title(f"COCO标注可视化: {image_info['file_name']} ({len(annotations)} 个标注)", 
                fontsize=14, fontweight='bold')
    
    # 绘制标注
    for ann in annotations:
        category_id = ann['category_id']
        category_name = category_id_to_info[category_id]['name']
        color = np.array(category_colors[category_id]) / 255.0
        
        # 绘制边界框
        if show_bbox and 'bbox' in ann:
            bbox = ann['bbox']  # [x, y, width, height]
            rect = patches.Rectangle(
                (bbox[0], bbox[1]), bbox[2], bbox[3],
                linewidth=3, edgecolor=color, facecolor='none'
            )
            ax.add_patch(rect)
            
            # 添加标签
            if show_labels:
                ax.text(bbox[0], bbox[1] - 8, f"{category_name}", 
                       bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.8),
                       fontsize=12, color='white', weight='bold')
        
        # 绘制分割掩码
        if show_segmentation and 'segmentation' in ann and ann['segmentation']:
            for seg in ann['segmentation']:
                if len(seg) >= 6:  # 至少3个点
                    # 将分割点转换为多边形
                    points = np.array(seg).reshape(-1, 2)
                    polygon = Polygon(points, closed=True, alpha=0.4, 
                                    facecolor=color, edgecolor=color, linewidth=2)
                    ax.add_patch(polygon)
    
    ax.set_xlim(0, image_info['width'])
    ax.set_ylim(image_info['height'], 0)
    ax.axis('off')
    
    plt.tight_layout()
    plt.show()


def visualize_random_samples(coco_data, images_dir, num_samples=4):
    """随机可视化多张图片"""
    print(f"\\n🎲 随机显示 {num_samples} 张图片...")
    
    if len(coco_data['images']) < num_samples:
        num_samples = len(coco_data['images'])
        print(f"⚠️  数据集只有 {len(coco_data['images'])} 张图片，将显示全部")
    
    selected_images = random.sample(coco_data['images'], num_samples)
    
    for i, image_info in enumerate(selected_images):
        print(f"\\n--- 第 {i+1}/{num_samples} 张图片 ---")
        visualize_image(coco_data, images_dir, image_id=image_info['id'])


def visualize_category_samples(coco_data, images_dir, category_name, num_samples=3):
    """可视化指定类别的样本"""
    
    # 找到类别ID
    category_id = None
    for cat in coco_data['categories']:
        if cat['name'] == category_name:
            category_id = cat['id']
            break
    
    if category_id is None:
        print(f"❌ 类别 '{category_name}' 不存在")
        available_categories = [cat['name'] for cat in coco_data['categories']]
        print(f"可用类别: {available_categories}")
        return
    
    # 找到包含该类别的图片
    image_ids_with_category = set()
    for ann in coco_data['annotations']:
        if ann['category_id'] == category_id:
            image_ids_with_category.add(ann['image_id'])
    
    if not image_ids_with_category:
        print(f"❌ 没有找到包含类别 '{category_name}' 的图片")
        return
    
    # 随机选择样本
    image_ids = list(image_ids_with_category)
    if len(image_ids) < num_samples:
        num_samples = len(image_ids)
        print(f"⚠️  类别 '{category_name}' 只有 {len(image_ids)} 张图片，将显示全部")
    
    selected_ids = random.sample(image_ids, num_samples)
    
    print(f"\\n🏷️  显示类别 '{category_name}' 的 {num_samples} 个样本...")
    
    for i, image_id in enumerate(selected_ids):
        print(f"\\n--- 类别 '{category_name}' 第 {i+1}/{num_samples} 个样本 ---")
        visualize_image(coco_data, images_dir, image_id=image_id)


def check_annotation_quality(coco_data):
    """检查标注质量"""
    print("\\n" + "="*60)
    print("🔍 标注质量检查")
    print("="*60)
    
    issues = []
    
    # 检查图片信息
    for img in coco_data['images']:
        if img['width'] <= 0 or img['height'] <= 0:
            issues.append(f"图片 {img['file_name']} 尺寸异常: {img['width']}x{img['height']}")
    
    # 检查标注信息
    for ann in coco_data['annotations']:
        # 检查边界框
        if 'bbox' in ann:
            bbox = ann['bbox']
            if bbox[2] <= 0 or bbox[3] <= 0:
                issues.append(f"标注 {ann['id']} 边界框尺寸异常: {bbox}")
            if bbox[0] < 0 or bbox[1] < 0:
                issues.append(f"标注 {ann['id']} 边界框位置异常: {bbox}")
        
        # 检查面积
        if 'area' in ann and ann['area'] <= 0:
            issues.append(f"标注 {ann['id']} 面积异常: {ann['area']}")
        
        # 检查类别ID
        valid_category_ids = {cat['id'] for cat in coco_data['categories']}
        if ann['category_id'] not in valid_category_ids:
            issues.append(f"标注 {ann['id']} 类别ID异常: {ann['category_id']}")
    
    if issues:
        print(f"❌ 发现 {len(issues)} 个问题:")
        for issue in issues[:10]:  # 只显示前10个问题
            print(f"   - {issue}")
        if len(issues) > 10:
            print(f"   ... 还有 {len(issues) - 10} 个问题")
    else:
        print("✅ 标注质量检查通过，未发现问题")
    
    print("="*60)


def main():
    """主函数"""
    
    # ==================== 配置参数 ====================
    
    # COCO标注文件路径
    ANNOTATION_FILE = r"C:\Users\<USER>\Desktop\datasets\coco_dataset\annotations\train.json"
    
    # 图片文件夹路径
    IMAGES_DIR = r"C:\Users\<USER>\Desktop\datasets\coco_dataset\images\train"
    
    # ==================== 执行可视化 ====================
    
    print("🎨 COCO数据集可视化工具")
    print("=" * 60)
    
    # 检查文件是否存在
    if not os.path.exists(ANNOTATION_FILE):
        print(f"❌ 标注文件不存在: {ANNOTATION_FILE}")
        print("请修改 ANNOTATION_FILE 路径")
        return
    
    if not os.path.exists(IMAGES_DIR):
        print(f"❌ 图片目录不存在: {IMAGES_DIR}")
        print("请修改 IMAGES_DIR 路径")
        return
    
    try:
        # 加载COCO数据
        print("📂 加载COCO数据...")
        coco_data = load_coco_data(ANNOTATION_FILE)
        
        # 显示数据集信息
        show_dataset_info(coco_data)
        
        # 检查标注质量
        check_annotation_quality(coco_data)
        
        # 可视化选项（取消注释来启用）
        
        # 1. 随机可视化几张图片
        visualize_random_samples(coco_data, IMAGES_DIR, num_samples=3)
        
        # 2. 可视化指定类别的样本（修改类别名称）
        # 获取第一个类别作为示例
        if coco_data['categories']:
            first_category = coco_data['categories'][0]['name']
            # visualize_category_samples(coco_data, IMAGES_DIR, first_category, num_samples=2)
        
        # 3. 可视化指定图片ID的图片
        # visualize_image(coco_data, IMAGES_DIR, image_id=0)
        
        # 4. 可视化指定文件名的图片
        # visualize_image(coco_data, IMAGES_DIR, image_filename="example.jpg")
        
        print("\\n✅ 可视化完成！")
        print("\\n💡 提示:")
        print("   - 取消注释上面的代码来启用更多可视化选项")
        print("   - 修改类别名称来查看特定类别的样本")
        print("   - 修改图片ID或文件名来查看特定图片")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()