#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ListFilePaths.py - 列出目录中所有文件的路径并保存到文本文件

这个脚本用于遍历指定目录及其所有子目录，收集所有文件的路径，
并将这些路径保存到文本文件中，每个路径占一行。

主要功能：
1. 递归遍历目录树，获取所有文件的完整路径
2. 将文件路径写入到指定的输出文件中
3. 支持命令行参数指定目录和输出文件

适用于需要获取目录结构、批量处理文件或创建文件清单的场景。
"""
import os

def list_files_to_text(directory_path, output_file):
    
    # 确保目录路径存在
    if not os.path.exists(directory_path):
        print(f"错误: 目录 '{directory_path}' 不存在")
        return False
    
    # 创建文件
    with open(output_file, 'w', encoding='utf-8') as f:
        # 遍历目录中的所有文件和子目录
        for root, dirs, files in os.walk(directory_path):
            # 处理每个文件
            for file in files:
                # 获取完整的文件路径
                file_path = os.path.join(root, file)
                # 将路径写入文件，每行一个
                f.write(f"{file_path}\n")
    
    print(f"文件路径已成功写入到 {output_file}")
    return True

if __name__ == "__main__":
    # 处理命令行参数或默认值，也可以通过命令行参数获取
    import sys
    
    if len(sys.argv) > 2:
        directory_path = sys.argv[1]
        output_file = sys.argv[2]
    else:
        # 默认使用当前目录和默认输出文件名
        directory_path = r"D:\Desktop\14-CDTQ"
        output_file = "file_paths.txt"
        print(f"使用默认参数: 目录='{directory_path}', 输出文件='{output_file}'")
        print("你可以通过命令行指定参数: python ListFilePaths.py <目录路径> <输出文件路径>")
    
    list_files_to_text(directory_path, output_file) 