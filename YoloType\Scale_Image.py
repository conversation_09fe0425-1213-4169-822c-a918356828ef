#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Scale_Image.py - 调整和合成图像

这个脚本用于调整图像大小并将调整后的图像粘贴到另一张图像上。
主要功能：
1. 将输入图像调整为指定尺寸
2. 将调整后的图像粘贴到另一个目标图像上的指定位置
3. 保存合成后的图像

适用于需要将一个图像（如图标、水印或缩略图）放置在另一个图像上的情况。
"""
from PIL import Image

def resize_and_paste(image1_path, image2_path, output_path):
    # 打开第一张图片并调整大小
    img1 = Image.open(image1_path)
    img1_resized = img1.resize((512, 520))

    # 打开第二张图片
    img2 = Image.open(image2_path)

    # 将调整大小后的图片贴到第二张图片的左上角
    img2.paste(img1_resized, (10, 60))

    # 保存结果
    img2.save(output_path)

# 使用示例
img1=r"D:\WH_WorkFiles\DeepLearn\ultralytics\Train_demo\runs\detect\predict3\16152852.png"
img2=r"D:\WH_WorkFiles\DeepLearn\ultralytics\Train_demo\runs\detect\predict3\16152852.jpg"
resize_and_paste(img1, img2, "output.jpg")