"""
COCO数据集验证工具

验证COCO格式数据集的完整性和正确性
检查文件结构、JSON格式、标注质量等
"""

import json
import os
import cv2
import numpy as np
from collections import defaultdict, Counter


class COCOValidator:
    """COCO数据集验证器"""
    
    def __init__(self, annotation_file, images_dir):
        """
        初始化验证器
        
        :param annotation_file: COCO标注JSON文件路径
        :param images_dir: 图片文件夹路径
        """
        self.annotation_file = annotation_file
        self.images_dir = images_dir
        self.coco_data = None
        self.validation_results = {
            'errors': [],
            'warnings': [],
            'info': []
        }
    
    def validate_all(self):
        """执行完整验证"""
        print("🔍 开始COCO数据集验证...")
        print("=" * 60)
        
        # 1. 验证文件存在性
        if not self._validate_files():
            return False
        
        # 2. 验证JSON格式
        if not self._validate_json_format():
            return False
        
        # 3. 验证COCO结构
        if not self._validate_coco_structure():
            return False
        
        # 4. 验证图片文件
        self._validate_image_files()
        
        # 5. 验证标注数据
        self._validate_annotations()
        
        # 6. 验证数据一致性
        self._validate_data_consistency()
        
        # 7. 显示验证结果
        self._show_validation_results()
        
        return len(self.validation_results['errors']) == 0
    
    def _validate_files(self):
        """验证文件存在性"""
        print("📁 检查文件存在性...")
        
        if not os.path.exists(self.annotation_file):
            self.validation_results['errors'].append(f"标注文件不存在: {self.annotation_file}")
            return False
        
        if not os.path.exists(self.images_dir):
            self.validation_results['errors'].append(f"图片目录不存在: {self.images_dir}")
            return False
        
        self.validation_results['info'].append("✅ 文件路径检查通过")
        return True
    
    def _validate_json_format(self):
        """验证JSON格式"""
        print("📄 检查JSON格式...")
        
        try:
            with open(self.annotation_file, 'r', encoding='utf-8') as f:
                self.coco_data = json.load(f)
            self.validation_results['info'].append("✅ JSON格式正确")
            return True
        except json.JSONDecodeError as e:
            self.validation_results['errors'].append(f"JSON格式错误: {e}")
            return False
        except Exception as e:
            self.validation_results['errors'].append(f"读取JSON文件失败: {e}")
            return False
    
    def _validate_coco_structure(self):
        """验证COCO数据结构"""
        print("🏗️  检查COCO数据结构...")
        
        required_keys = ['images', 'annotations', 'categories']
        optional_keys = ['info', 'licenses']
        
        # 检查必需字段
        for key in required_keys:
            if key not in self.coco_data:
                self.validation_results['errors'].append(f"缺少必需字段: {key}")
                return False
            if not isinstance(self.coco_data[key], list):
                self.validation_results['errors'].append(f"字段 {key} 必须是列表类型")
                return False
        
        # 检查可选字段
        for key in optional_keys:
            if key in self.coco_data:
                if key == 'info' and not isinstance(self.coco_data[key], dict):
                    self.validation_results['warnings'].append(f"字段 {key} 应该是字典类型")
                elif key == 'licenses' and not isinstance(self.coco_data[key], list):
                    self.validation_results['warnings'].append(f"字段 {key} 应该是列表类型")
        
        # 检查数据是否为空
        if len(self.coco_data['images']) == 0:
            self.validation_results['warnings'].append("images列表为空")
        if len(self.coco_data['categories']) == 0:
            self.validation_results['errors'].append("categories列表为空")
            return False
        
        self.validation_results['info'].append("✅ COCO数据结构正确")
        return True
    
    def _validate_image_files(self):
        """验证图片文件"""
        print("🖼️  检查图片文件...")
        
        missing_files = []
        invalid_images = []
        size_mismatches = []
        
        for img_info in self.coco_data['images']:
            # 检查必需字段
            required_fields = ['id', 'file_name', 'width', 'height']
            for field in required_fields:
                if field not in img_info:
                    self.validation_results['errors'].append(f"图片信息缺少字段 {field}: {img_info}")
                    continue
            
            if 'file_name' not in img_info:
                continue
            
            # 检查文件是否存在
            image_path = os.path.join(self.images_dir, img_info['file_name'])
            if not os.path.exists(image_path):
                missing_files.append(img_info['file_name'])
                continue
            
            # 检查图片是否可读取
            try:
                img = cv2.imread(image_path)
                if img is None:
                    invalid_images.append(img_info['file_name'])
                    continue
                
                # 检查尺寸是否匹配
                actual_height, actual_width = img.shape[:2]
                if actual_width != img_info['width'] or actual_height != img_info['height']:
                    size_mismatches.append({
                        'file': img_info['file_name'],
                        'recorded': (img_info['width'], img_info['height']),
                        'actual': (actual_width, actual_height)
                    })
            except Exception as e:
                invalid_images.append(f"{img_info['file_name']} (错误: {e})")
        
        # 记录结果
        if missing_files:
            self.validation_results['errors'].extend([f"图片文件不存在: {f}" for f in missing_files[:10]])
            if len(missing_files) > 10:
                self.validation_results['errors'].append(f"... 还有 {len(missing_files) - 10} 个文件不存在")
        
        if invalid_images:
            self.validation_results['errors'].extend([f"无法读取图片: {f}" for f in invalid_images[:10]])
            if len(invalid_images) > 10:
                self.validation_results['errors'].append(f"... 还有 {len(invalid_images) - 10} 个图片无法读取")
        
        if size_mismatches:
            for mismatch in size_mismatches[:5]:
                self.validation_results['warnings'].append(
                    f"图片尺寸不匹配: {mismatch['file']} "
                    f"记录={mismatch['recorded']} 实际={mismatch['actual']}"
                )
            if len(size_mismatches) > 5:
                self.validation_results['warnings'].append(f"... 还有 {len(size_mismatches) - 5} 个尺寸不匹配")
        
        if not missing_files and not invalid_images:
            self.validation_results['info'].append("✅ 图片文件检查通过")
    
    def _validate_annotations(self):
        """验证标注数据"""
        print("🏷️  检查标注数据...")
        
        # 获取有效的图片ID和类别ID
        valid_image_ids = {img['id'] for img in self.coco_data['images']}
        valid_category_ids = {cat['id'] for cat in self.coco_data['categories']}
        
        invalid_annotations = []
        bbox_issues = []
        segmentation_issues = []
        
        for ann in self.coco_data['annotations']:
            # 检查必需字段
            required_fields = ['id', 'image_id', 'category_id']
            for field in required_fields:
                if field not in ann:
                    invalid_annotations.append(f"标注 {ann.get('id', 'unknown')} 缺少字段 {field}")
                    continue
            
            # 检查图片ID是否有效
            if ann['image_id'] not in valid_image_ids:
                invalid_annotations.append(f"标注 {ann['id']} 引用了无效的图片ID: {ann['image_id']}")
            
            # 检查类别ID是否有效
            if ann['category_id'] not in valid_category_ids:
                invalid_annotations.append(f"标注 {ann['id']} 引用了无效的类别ID: {ann['category_id']}")
            
            # 检查边界框
            if 'bbox' in ann:
                bbox = ann['bbox']
                if not isinstance(bbox, list) or len(bbox) != 4:
                    bbox_issues.append(f"标注 {ann['id']} 边界框格式错误: {bbox}")
                else:
                    x, y, w, h = bbox
                    if w <= 0 or h <= 0:
                        bbox_issues.append(f"标注 {ann['id']} 边界框尺寸无效: {bbox}")
                    if x < 0 or y < 0:
                        bbox_issues.append(f"标注 {ann['id']} 边界框位置无效: {bbox}")
            
            # 检查分割数据
            if 'segmentation' in ann and ann['segmentation']:
                if isinstance(ann['segmentation'], list):
                    for i, seg in enumerate(ann['segmentation']):
                        if isinstance(seg, list):
                            if len(seg) < 6 or len(seg) % 2 != 0:
                                segmentation_issues.append(
                                    f"标注 {ann['id']} 分割数据 {i} 格式错误: 长度={len(seg)}"
                                )
            
            # 检查面积
            if 'area' in ann and ann['area'] <= 0:
                invalid_annotations.append(f"标注 {ann['id']} 面积无效: {ann['area']}")
        
        # 记录结果
        if invalid_annotations:
            self.validation_results['errors'].extend(invalid_annotations[:10])
            if len(invalid_annotations) > 10:
                self.validation_results['errors'].append(f"... 还有 {len(invalid_annotations) - 10} 个标注问题")
        
        if bbox_issues:
            self.validation_results['warnings'].extend(bbox_issues[:10])
            if len(bbox_issues) > 10:
                self.validation_results['warnings'].append(f"... 还有 {len(bbox_issues) - 10} 个边界框问题")
        
        if segmentation_issues:
            self.validation_results['warnings'].extend(segmentation_issues[:5])
            if len(segmentation_issues) > 5:
                self.validation_results['warnings'].append(f"... 还有 {len(segmentation_issues) - 5} 个分割问题")
        
        if not invalid_annotations and not bbox_issues and not segmentation_issues:
            self.validation_results['info'].append("✅ 标注数据检查通过")
    
    def _validate_data_consistency(self):
        """验证数据一致性"""
        print("🔗 检查数据一致性...")
        
        # 检查ID唯一性
        image_ids = [img['id'] for img in self.coco_data['images']]
        annotation_ids = [ann['id'] for ann in self.coco_data['annotations']]
        category_ids = [cat['id'] for cat in self.coco_data['categories']]
        
        # 检查重复ID
        duplicate_image_ids = [id for id, count in Counter(image_ids).items() if count > 1]
        duplicate_annotation_ids = [id for id, count in Counter(annotation_ids).items() if count > 1]
        duplicate_category_ids = [id for id, count in Counter(category_ids).items() if count > 1]
        
        if duplicate_image_ids:
            self.validation_results['errors'].append(f"重复的图片ID: {duplicate_image_ids}")
        if duplicate_annotation_ids:
            self.validation_results['errors'].append(f"重复的标注ID: {duplicate_annotation_ids}")
        if duplicate_category_ids:
            self.validation_results['errors'].append(f"重复的类别ID: {duplicate_category_ids}")
        
        # 检查孤立的标注（没有对应图片的标注）
        orphaned_annotations = []
        valid_image_ids = set(image_ids)
        for ann in self.coco_data['annotations']:
            if ann['image_id'] not in valid_image_ids:
                orphaned_annotations.append(ann['id'])
        
        if orphaned_annotations:
            self.validation_results['warnings'].append(f"孤立的标注（无对应图片）: {orphaned_annotations[:10]}")
        
        # 检查没有标注的图片
        annotated_image_ids = set(ann['image_id'] for ann in self.coco_data['annotations'])
        unannotated_images = [id for id in image_ids if id not in annotated_image_ids]
        
        if unannotated_images:
            self.validation_results['info'].append(f"没有标注的图片数量: {len(unannotated_images)}")
        
        if not duplicate_image_ids and not duplicate_annotation_ids and not duplicate_category_ids:
            self.validation_results['info'].append("✅ 数据一致性检查通过")
    
    def _show_validation_results(self):
        """显示验证结果"""
        print("\\n" + "="*60)
        print("📋 验证结果汇总")
        print("="*60)
        
        # 显示基本统计
        print(f"📊 数据集统计:")
        print(f"   图片数量: {len(self.coco_data['images'])}")
        print(f"   标注数量: {len(self.coco_data['annotations'])}")
        print(f"   类别数量: {len(self.coco_data['categories'])}")
        
        # 显示类别信息
        print(f"\\n🏷️  类别列表:")
        for cat in self.coco_data['categories']:
            print(f"   {cat['id']:>2}: {cat['name']}")
        
        # 显示验证结果
        print(f"\\n🔍 验证结果:")
        print(f"   ✅ 信息: {len(self.validation_results['info'])}")
        print(f"   ⚠️  警告: {len(self.validation_results['warnings'])}")
        print(f"   ❌ 错误: {len(self.validation_results['errors'])}")
        
        # 显示详细信息
        if self.validation_results['info']:
            print(f"\\n✅ 信息:")
            for info in self.validation_results['info']:
                print(f"   {info}")
        
        if self.validation_results['warnings']:
            print(f"\\n⚠️  警告:")
            for warning in self.validation_results['warnings']:
                print(f"   {warning}")
        
        if self.validation_results['errors']:
            print(f"\\n❌ 错误:")
            for error in self.validation_results['errors']:
                print(f"   {error}")
        
        # 总结
        print(f"\\n" + "="*60)
        if len(self.validation_results['errors']) == 0:
            print("🎉 验证通过！数据集格式正确")
        else:
            print("❌ 验证失败！请修复上述错误")
        print("="*60)
    
    def get_validation_report(self):
        """获取验证报告"""
        return {
            'dataset_info': {
                'annotation_file': self.annotation_file,
                'images_dir': self.images_dir,
                'total_images': len(self.coco_data['images']) if self.coco_data else 0,
                'total_annotations': len(self.coco_data['annotations']) if self.coco_data else 0,
                'total_categories': len(self.coco_data['categories']) if self.coco_data else 0
            },
            'validation_results': self.validation_results,
            'is_valid': len(self.validation_results['errors']) == 0
        }


def main():
    """主函数"""
    
    # ==================== 配置参数 ====================
    
    # COCO标注文件路径
    ANNOTATION_FILE = r"E:\datasets\coco_dataset\annotations\train.json"
    
    # 图片文件夹路径
    IMAGES_DIR = r"E:\datasets\coco_dataset\images\train"
    
    # ==================== 执行验证 ====================
    
    print("🔍 COCO数据集验证工具")
    print("=" * 60)
    print(f"标注文件: {ANNOTATION_FILE}")
    print(f"图片目录: {IMAGES_DIR}")
    print("=" * 60)
    
    try:
        # 创建验证器
        validator = COCOValidator(ANNOTATION_FILE, IMAGES_DIR)
        
        # 执行验证
        is_valid = validator.validate_all()
        
        # 获取验证报告
        report = validator.get_validation_report()
        
        # 保存验证报告（可选）
        # report_file = "validation_report.json"
        # with open(report_file, 'w', encoding='utf-8') as f:
        #     json.dump(report, f, indent=2, ensure_ascii=False)
        # print(f"\\n📄 验证报告已保存到: {report_file}")
        
        return is_valid
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()