# Halcon OCR转PPOCR标注格式转换工具

这个工具用于将Halcon OCR标注文件转换为PaddleOCR (PPOCR)格式的标注文件。

## 功能特点

- 支持将Halcon OCR旋转矩形框转换为PPOCR的四点多边形格式
- 自动生成检测（det）和识别（rec）两种格式的标注文件
- 自动划分训练集、验证集和测试集（比例可调整）

## 使用方法

有两种使用方式：

### 1. 直接运行脚本

```bash
python convert_halcon_to_ppocr.py --input HalconOCR.json --output output_dir
```

参数说明：
- `--input` 或 `-i`: Halcon OCR JSON标注文件路径
- `--output` 或 `-o`: 输出目录路径，将在此目录下生成det和rec两个子目录
- `--config` 或 `-c`: 可选，指定配置文件路径

### 2. 使用配置文件

编辑`config.json`文件，设置相应参数：

```json
{
    "input": "HalconOCR.json",
    "output": "ppocr_output",
    "train_ratio": 0.8,
    "val_ratio": 0.1,
    "test_ratio": 0.1,
    "random_seed": 42,
    "crop_image": false,
    "crop_dir": "crops"
}
```

然后运行：

```bash
python convert_halcon_to_ppocr.py --config config.json
```

或直接双击`run_convert.bat`脚本执行。

## 输出说明

转换后会在指定的输出目录下生成以下文件：

```
output_dir/
├── det/
│   ├── train.txt
│   ├── val.txt
│   └── test.txt
└── rec/
    ├── train.txt
    ├── val.txt
    └── test.txt
```

### 检测标注格式(det)

每行格式为：`图像路径\t[标注JSON]`

标注JSON格式：
```json
[
  {
    "transcription": "文字内容",
    "points": [[x1, y1], [x2, y2], [x3, y3], [x4, y4]],
    "difficult": false
  },
  ...
]
```

### 识别标注格式(rec)

每行格式为：`图像路径\t文字内容`

## 注意事项

1. 对于识别标注，脚本假设使用裁剪后的文本行图像，实际应用时需要根据检测框裁剪图像
2. 默认按照80%训练集、10%验证集、10%测试集划分数据，可以在代码中调整
3. 需要安装numpy依赖库 (`pip install numpy`)

## 技术细节

- Halcon OCR使用旋转矩形表示文本区域，需要转换为四点坐标表示
- 转换使用矩形中心点、宽度、高度和旋转角度计算四个顶点坐标
- 生成的顶点坐标顺序为：左上、右上、右下、左下