import json
import os
import cv2
from datetime import datetime


def convert_yolo_to_coco(images_dir, labels_dir, class_names, output_path, task_type="detection"):
    """
    简化版YOLO到COCO转换函数
    
    :param images_dir: 图片文件夹路径
    :param labels_dir: YOLO标注文件夹路径
    :param class_names: 类别名称列表
    :param output_path: 输出COCO JSON文件路径
    :param task_type: 任务类型，"detection" 或 "segmentation"
    """
    
    if task_type == "detection":
        return _yolo_det_to_coco(images_dir, labels_dir, class_names, output_path)
    elif task_type == "segmentation":
        return _yolo_seg_to_coco(images_dir, labels_dir, class_names, output_path)
    else:
        raise ValueError("task_type 必须是 'detection' 或 'segmentation'")


def _create_coco_structure(class_names):
    """创建COCO数据结构"""
    coco_data = {
        "info": {
            "description": "YOLO to COCO conversion",
            "version": "1.0",
            "year": datetime.now().year,
            "contributor": "Auto-generated",
            "date_created": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        "licenses": [{"id": 1, "name": "Unknown", "url": ""}],
        "images": [],
        "annotations": [],
        "categories": []
    }
    
    # 添加类别
    for i, class_name in enumerate(class_names):
        coco_data["categories"].append({
            "id": i,
            "name": class_name,
            "supercategory": "object"
        })
    
    return coco_data


def _get_image_files(images_dir):
    """获取图片文件列表"""
    image_files = []
    supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    
    for file in os.listdir(images_dir):
        if any(file.lower().endswith(ext) for ext in supported_formats):
            image_files.append(file)
    
    return sorted(image_files)


def _yolo_det_to_coco(images_dir, labels_dir, class_names, output_path):
    """YOLO检测格式转COCO"""
    coco_data = _create_coco_structure(class_names)
    annotation_id = 0
    
    image_files = _get_image_files(images_dir)
    
    for image_id, image_file in enumerate(image_files):
        image_path = os.path.join(images_dir, image_file)
        
        # 获取图片尺寸
        img = cv2.imread(image_path)
        if img is None:
            print(f"警告：无法读取图片 {image_file}")
            continue
        
        height, width = img.shape[:2]
        
        # 添加图片信息
        coco_data["images"].append({
            "id": image_id,
            "width": width,
            "height": height,
            "file_name": image_file,
            "license": 1,
            "date_captured": ""
        })
        
        # 处理对应的标注文件
        label_file = os.path.splitext(image_file)[0] + '.txt'
        label_path = os.path.join(labels_dir, label_file)
        
        if os.path.exists(label_path):
            with open(label_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    parts = line.split()
                    if len(parts) < 5:
                        continue
                    
                    class_id = int(parts[0])
                    cx, cy, w, h = map(float, parts[1:5])
                    
                    # YOLO格式转COCO格式
                    x = (cx - w/2) * width
                    y = (cy - h/2) * height
                    bbox_width = w * width
                    bbox_height = h * height
                    
                    # 边界检查
                    x = max(0, x)
                    y = max(0, y)
                    bbox_width = min(bbox_width, width - x)
                    bbox_height = min(bbox_height, height - y)
                    
                    coco_data["annotations"].append({
                        "id": annotation_id,
                        "image_id": image_id,
                        "category_id": class_id,
                        "bbox": [x, y, bbox_width, bbox_height],
                        "area": bbox_width * bbox_height,
                        "segmentation": [],
                        "iscrowd": 0
                    })
                    annotation_id += 1
    
    # 保存文件
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(coco_data, f, indent=2, ensure_ascii=False)
    
    print(f"检测任务转换完成！")
    print(f"图片数量: {len(coco_data['images'])}")
    print(f"标注数量: {len(coco_data['annotations'])}")
    print(f"输出文件: {output_path}")
    
    return coco_data


def _yolo_seg_to_coco(images_dir, labels_dir, class_names, output_path):
    """YOLO分割格式转COCO"""
    coco_data = _create_coco_structure(class_names)
    annotation_id = 0
    
    image_files = _get_image_files(images_dir)
    
    for image_id, image_file in enumerate(image_files):
        image_path = os.path.join(images_dir, image_file)
        
        # 获取图片尺寸
        img = cv2.imread(image_path)
        if img is None:
            print(f"警告：无法读取图片 {image_file}")
            continue
        
        height, width = img.shape[:2]
        
        # 添加图片信息
        coco_data["images"].append({
            "id": image_id,
            "width": width,
            "height": height,
            "file_name": image_file,
            "license": 1,
            "date_captured": ""
        })
        
        # 处理对应的标注文件
        label_file = os.path.splitext(image_file)[0] + '.txt'
        label_path = os.path.join(labels_dir, label_file)
        
        if os.path.exists(label_path):
            with open(label_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    parts = line.split()
                    if len(parts) < 7:  # 至少需要类别 + 3个点
                        continue
                    
                    class_id = int(parts[0])
                    coords = list(map(float, parts[1:]))
                    
                    if len(coords) % 2 != 0:
                        continue
                    
                    # 转换为像素坐标
                    segmentation = []
                    for i in range(0, len(coords), 2):
                        x = coords[i] * width
                        y = coords[i+1] * height
                        segmentation.extend([x, y])
                    
                    # 计算边界框
                    x_coords = segmentation[::2]
                    y_coords = segmentation[1::2]
                    
                    x_min, x_max = min(x_coords), max(x_coords)
                    y_min, y_max = min(y_coords), max(y_coords)
                    
                    bbox_width = x_max - x_min
                    bbox_height = y_max - y_min
                    
                    coco_data["annotations"].append({
                        "id": annotation_id,
                        "image_id": image_id,
                        "category_id": class_id,
                        "bbox": [x_min, y_min, bbox_width, bbox_height],
                        "area": bbox_width * bbox_height,
                        "segmentation": [segmentation],
                        "iscrowd": 0
                    })
                    annotation_id += 1
    
    # 保存文件
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(coco_data, f, indent=2, ensure_ascii=False)
    
    print(f"分割任务转换完成！")
    print(f"图片数量: {len(coco_data['images'])}")
    print(f"标注数量: {len(coco_data['annotations'])}")
    print(f"输出文件: {output_path}")
    
    return coco_data


# 使用示例
if __name__ == "__main__":
    # 示例用法
    images_dir = "path/to/images"
    labels_dir = "path/to/yolo/labels"
    class_names = ["person", "car", "bicycle"]
    output_path = "annotations.json"
    
    # 检测任务转换
    # convert_yolo_to_coco(images_dir, labels_dir, class_names, output_path, "detection")
    
    # 分割任务转换
    # convert_yolo_to_coco(images_dir, labels_dir, class_names, output_path, "segmentation")
    
    print("请在代码中设置正确的路径和类别名称后运行转换函数")