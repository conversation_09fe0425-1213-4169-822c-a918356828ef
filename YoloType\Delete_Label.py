#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Delete_Label.py - 从YOLO格式标签文件中删除指定类别的标签

这个脚本用于从YOLO格式的标签文件中删除指定类别的标签数据。
提供了两种功能：
1. 根据类别ID直接删除特定类别的标签
2. 根据边界框宽度与参考类别（类别0）的宽度比例过滤标签

这对于清理数据集、去除不需要的类别或过滤掉尺寸较小的标签很有用。
"""
import os

def remove_label_2_from_files(label_dir,target):
    # 获取所有标签文件
    label_files = [f for f in os.listdir(label_dir) if f.endswith('.txt')]
    
    for label_file in label_files:
        label_path = os.path.join(label_dir, label_file)
        # 读取标签文件
        with open(label_path, 'r') as file:
            lines = file.readlines()
        
        # 过滤掉标签为2的行
        filtered_lines = [line for line in lines if not line.startswith(target)]
        
        # 如果过滤后的行数与原行数不同，则写入文件
        if len(filtered_lines) != len(lines):
            with open(label_path, 'w') as file:
                file.writelines(filtered_lines)
            print(f"Updated label file: {label_path}")


import os

def filter_labels_based_on_size(label_dir):
    label_files = [f for f in os.listdir(label_dir) if f.endswith('.txt')]
    
    for label_file in label_files:
        label_path = os.path.join(label_dir, label_file)
        with open(label_path, 'r') as file:
            lines = file.readlines()
        
        # Find the width of label 0 bounding box
        reference_width = None
        filtered_lines = []

        for line in lines:
            parts = line.strip().split()
            label = int(parts[0])
            width = float(parts[3])  # YOLO format: class, x_center, y_center, width, height

            if label == 0:
                if reference_width is None or width > reference_width:
                    reference_width = width

        if reference_width is not None:
            half_reference_width = reference_width / 2

            # Filter lines based on width
            for line in lines:
                parts = line.strip().split()
                label = int(parts[0])
                width = float(parts[3])

                # Retain lines that are not label 0 or have width >= half of label 0 width
                if label == 0 or width >= half_reference_width:
                    filtered_lines.append(line)

            if len(filtered_lines) != len(lines):
                with open(label_path, 'w') as file:
                    file.writelines(filtered_lines)
                print(f"Updated label file: {label_path}")



# 示例
label_directory = r'D:\WH_WorkFiles\ProjectFile\03RPA\DataSets\Datasets1112\val'

target='1'

# 删除指定类别的标签
# filter_labels_based_on_size(label_directory)


remove_label_2_from_files(label_directory,target)
