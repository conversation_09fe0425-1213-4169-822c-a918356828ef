#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ConvertToSegmentation.py - 将YOLO目标检测标注转换为分割标注

这个脚本用于将YOLO格式的目标检测标注(边界框)转换为多边形分割标注。
它将矩形边界框的四个角点坐标保存为分割标注，可用于训练分割模型。

转换后的格式为每行：class_id x1 y1 x2 y2 x3 y3 x4 y4
其中(x1,y1)到(x4,y4)是矩形的四个角点，按照左上、右上、右下、左下顺序排列。
"""
# --*-- coding:gbk --*--
import os
import cv2
import numpy as np
from tqdm import tqdm

def convert_to_segmentation(image_path, label_path, output_path, image_size):
    """
    将YOLO目标检测标注转换为分割标注
    :param image_path: 图片路径
    :param label_path: 标注文件路径
    :param output_path: 输出标注路径
    :param image_size: 图片尺寸 (width, height)
    """
    # 读取标注文件
    if os.path.exists(label_path):
        with open(label_path, 'r') as f:
            lines = f.readlines()
        
        # 创建新的标注文件
        with open(output_path, 'w') as f:
            for line in lines:
                # 解析YOLO格式标注
                class_id, x_center, y_center, width, height = map(float, line.strip().split())
                
                # 将YOLO格式转换为多边形标注
                x1 = (x_center - width/2)
                y1 = (y_center - height/2)
                x2 = (x_center + width/2)
                y2 = (y_center + height/2)
                
                # 获取矩形的四个角点（第一个点是左上角）
                points = [
                    [x1, y1],  # 左上
                    [x2, y1],  # 右上
                    [x2, y2],  # 右下
                    [x1, y2]   # 左下
                ]
                
                # 将多边形转换为YOLO格式标注（第一个点是左上角）
                points_str = ' '.join([f"{x:.6f} {y:.6f}" for x, y in points])
                
                # 写入标注文件格式：class_id x1 y1 x2 y2 x3 y3 x4 y4 ...
                f.write(f"{int(class_id)} {points_str}\n")
                
                # 打印标注信息
                print(f"标注信息: 类别={class_id}, 坐标={points_str}")
    
    return True

def main():
    # 输入路径
    image_dir = r"E:\WH_WorkFiles\ProjectFile\03RPA\DataSets\Side\seg\images\val"  # 图片目录
    label_dir = r"E:\WH_WorkFiles\ProjectFile\03RPA\DataSets\Side\seg\labels\val"  # 标注目录
    output_dir = r"E:\WH_WorkFiles\ProjectFile\03RPA\DataSets\Side\seg\labels_seg\val"  # 输出目录
    
    # 检查图片目录是否存在
    if not os.path.exists(image_dir):
        print(f"错误：图片目录不存在: {image_dir}")
        return
    if not os.path.exists(label_dir):
        print(f"错误：标注目录不存在: {label_dir}")
        return
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有图片文件
    image_files = [f for f in os.listdir(image_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png','.bmp'))]
    
    if not image_files:
        print(f"错误：目录 {image_dir} 没有找到图片文件")
        print("支持的图片格式：.jpg, .jpeg, .png, .bmp")
        return
    
    print(f"找到 {len(image_files)} 个图片文件")
    
    success_count = 0
    for image_file in tqdm(image_files, desc="Converting annotations"):
        # 获取文件路径
        image_path = os.path.join(image_dir, image_file)
        label_path = os.path.join(label_dir, os.path.splitext(image_file)[0] + '.txt')
        output_path = os.path.join(output_dir, os.path.splitext(image_file)[0] + '.txt')
        
        # 检查标注文件是否存在
        if not os.path.exists(label_path):
            print(f"错误：找不到标注文件: {label_path}")
            continue
        
        # 读取图片尺寸
        img = cv2.imread(image_path)
        if img is None:
            print(f"错误：无法读取图片: {image_path}")
            continue
            
        image_size = (img.shape[1], img.shape[0])
        
        # 转换标注
        if convert_to_segmentation(image_path, label_path, output_path, image_size):
            success_count += 1
    
    print(f"转换完成，成功转换 {success_count}/{len(image_files)} 个文件")
    print(f"分割标注文件目录：{output_dir}")

def detect_vertical_lines(image_path):
    # 读取图片
    img = cv2.imread(image_path)
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 使用Sobel算子检测垂直边缘
    sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=5)
    
    # 转换为绝对值，并归一化
    abs_sobelx = np.absolute(sobelx)
    scaled_sobel = np.uint8(255 * abs_sobelx / np.max(abs_sobelx))
    
    # 应用阈值
    thresh_min = 100
    thresh_max = 255
    binary_output = np.zeros_like(scaled_sobel)
    binary_output[(scaled_sobel >= thresh_min) & (scaled_sobel <= thresh_max)] = 1
    
    # 寻找轮廓
    contours, _ = cv2.findContours(binary_output.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 绘制轮廓
    result = img.copy()
    for contour in contours:
        # 获取轮廓的外接矩形，宽高比接近1:2
        x, y, w, h = cv2.boundingRect(contour)
        if h > img.shape[0] * 0.5 and w < img.shape[1] * 0.1:  # 检测到可能是垂直线
            cv2.rectangle(result, (x, y), (x+w, y+h), (0, 255, 0), 2)
    
    cv2.imshow("检测结果", result)
    cv2.waitKey(0)

if __name__ == "__main__":
    detect_vertical_lines(r"D:\Desktop\0313crop")