#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Delete_NOIMageLabel.py - 删除没有对应图像的标签文件

这个脚本用于清理YOLO数据集中的标签文件，删除那些没有对应图像文件的标签文件。
当数据集中的图像文件和标签文件不完全匹配时，这个脚本可以帮助保持数据集的一致性。

工作流程：
1. 扫描图像目录中的所有图像文件
2. 扫描标签目录中的所有标签文件
3. 找出没有对应图像的标签文件
4. 删除这些多余的标签文件
"""
import os

def clean_yolo_labels(image_dir, label_dir):
    # 获取图像和标签文件的集合
    image_files = {os.path.splitext(f)[0] for f in os.listdir(image_dir) if f.endswith(('.jpg', '.png','bmp'))}
    label_files = {os.path.splitext(f)[0] for f in os.listdir(label_dir) if f.endswith('.txt')}
    
    # 找出没有对应图像的标签文件
    extra_labels = label_files - image_files
    
    # 删除多余的标签文件
    for label in extra_labels:
        label_path = os.path.join(label_dir, label + '.txt')
        os.remove(label_path)
        print(f"Deleted label file: {label_path}")

# 使用示例
image_directory = r'D:\Desktop\crop5'
label_directory = r'D:\Desktop\labels'
# 删除没有对应图像的标签文件
clean_yolo_labels(image_directory, label_directory)
