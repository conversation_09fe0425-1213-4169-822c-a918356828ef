import json
import cv2
import numpy as np
import os
import random
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Polygon
from PIL import Image, ImageDraw, ImageFont
import argparse


class COCOVisualizer:
    """COCO数据集可视化工具"""
    
    def __init__(self, annotation_file, images_dir):
        """
        初始化可视化器
        
        :param annotation_file: COCO标注JSON文件路径
        :param images_dir: 图片文件夹路径
        """
        self.annotation_file = annotation_file
        self.images_dir = images_dir
        
        # 加载COCO数据
        self.coco_data = self._load_coco_data()
        
        # 创建索引
        self.image_id_to_info = {img['id']: img for img in self.coco_data['images']}
        self.category_id_to_info = {cat['id']: cat for cat in self.coco_data['categories']}
        
        # 按图片ID分组标注
        self.image_id_to_annotations = {}
        for ann in self.coco_data['annotations']:
            image_id = ann['image_id']
            if image_id not in self.image_id_to_annotations:
                self.image_id_to_annotations[image_id] = []
            self.image_id_to_annotations[image_id].append(ann)
        
        # 生成类别颜色
        self.category_colors = self._generate_colors()
        
        print(f"✅ COCO数据集加载完成")
        print(f"   📊 图片数量: {len(self.coco_data['images'])}")
        print(f"   📊 标注数量: {len(self.coco_data['annotations'])}")
        print(f"   📊 类别数量: {len(self.coco_data['categories'])}")
    
    def _load_coco_data(self):
        """加载COCO数据"""
        if not os.path.exists(self.annotation_file):
            raise FileNotFoundError(f"标注文件不存在: {self.annotation_file}")
        
        with open(self.annotation_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _generate_colors(self):
        """为每个类别生成颜色"""
        colors = {}
        for category in self.coco_data['categories']:
            # 生成随机颜色
            color = (
                random.randint(0, 255),
                random.randint(0, 255),
                random.randint(0, 255)
            )
            colors[category['id']] = color
        return colors
    
    def get_dataset_statistics(self):
        """获取数据集统计信息"""
        stats = {
            'total_images': len(self.coco_data['images']),
            'total_annotations': len(self.coco_data['annotations']),
            'total_categories': len(self.coco_data['categories']),
            'category_stats': {},
            'image_stats': {
                'min_annotations': float('inf'),
                'max_annotations': 0,
                'avg_annotations': 0
            }
        }
        
        # 统计每个类别的标注数量
        for category in self.coco_data['categories']:
            cat_id = category['id']
            cat_name = category['name']
            count = sum(1 for ann in self.coco_data['annotations'] if ann['category_id'] == cat_id)
            stats['category_stats'][cat_name] = {
                'id': cat_id,
                'count': count,
                'percentage': count / stats['total_annotations'] * 100 if stats['total_annotations'] > 0 else 0
            }
        
        # 统计每张图片的标注数量
        annotation_counts = []
        for image_id in self.image_id_to_info.keys():
            count = len(self.image_id_to_annotations.get(image_id, []))
            annotation_counts.append(count)
            stats['image_stats']['min_annotations'] = min(stats['image_stats']['min_annotations'], count)
            stats['image_stats']['max_annotations'] = max(stats['image_stats']['max_annotations'], count)
        
        if annotation_counts:
            stats['image_stats']['avg_annotations'] = sum(annotation_counts) / len(annotation_counts)
        else:
            stats['image_stats']['min_annotations'] = 0
        
        return stats
    
    def print_statistics(self):
        """打印数据集统计信息"""
        stats = self.get_dataset_statistics()
        
        print("\\n" + "="*60)
        print("📊 COCO数据集统计信息")
        print("="*60)
        
        print(f"📁 数据集概览:")
        print(f"   总图片数量: {stats['total_images']}")
        print(f"   总标注数量: {stats['total_annotations']}")
        print(f"   总类别数量: {stats['total_categories']}")
        
        print(f"\\n📈 图片标注统计:")
        print(f"   最少标注数: {stats['image_stats']['min_annotations']}")
        print(f"   最多标注数: {stats['image_stats']['max_annotations']}")
        print(f"   平均标注数: {stats['image_stats']['avg_annotations']:.2f}")
        
        print(f"\\n🏷️  类别分布:")
        for cat_name, cat_info in stats['category_stats'].items():
            print(f"   {cat_name:>15} (ID:{cat_info['id']:>2}): {cat_info['count']:>5} 个标注 ({cat_info['percentage']:>5.1f}%)")
        
        print("="*60)
    
    def visualize_image(self, image_id=None, image_filename=None, save_path=None, show_bbox=True, 
                       show_segmentation=True, show_labels=True, bbox_thickness=2):
        """
        可视化单张图片的标注
        
        :param image_id: 图片ID
        :param image_filename: 图片文件名
        :param save_path: 保存路径，如果为None则显示图片
        :param show_bbox: 是否显示边界框
        :param show_segmentation: 是否显示分割掩码
        :param show_labels: 是否显示标签
        :param bbox_thickness: 边界框线条粗细
        """
        # 获取图片信息
        if image_id is not None:
            if image_id not in self.image_id_to_info:
                print(f"❌ 图片ID {image_id} 不存在")
                return
            image_info = self.image_id_to_info[image_id]
        elif image_filename is not None:
            image_info = None
            for img in self.coco_data['images']:
                if img['file_name'] == image_filename:
                    image_info = img
                    image_id = img['id']
                    break
            if image_info is None:
                print(f"❌ 图片文件 {image_filename} 不存在")
                return
        else:
            print("❌ 请提供image_id或image_filename")
            return
        
        # 加载图片
        image_path = os.path.join(self.images_dir, image_info['file_name'])
        if not os.path.exists(image_path):
            print(f"❌ 图片文件不存在: {image_path}")
            return
        
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 获取该图片的所有标注
        annotations = self.image_id_to_annotations.get(image_id, [])
        
        print(f"🖼️  可视化图片: {image_info['file_name']}")
        print(f"   图片尺寸: {image_info['width']} x {image_info['height']}")
        print(f"   标注数量: {len(annotations)}")
        
        # 创建matplotlib图形
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(image)
        ax.set_title(f"COCO Visualization: {image_info['file_name']} ({len(annotations)} annotations)")
        
        # 绘制标注
        for ann in annotations:
            category_id = ann['category_id']
            category_name = self.category_id_to_info[category_id]['name']
            color = np.array(self.category_colors[category_id]) / 255.0
            
            # 绘制边界框
            if show_bbox and 'bbox' in ann:
                bbox = ann['bbox']  # [x, y, width, height]
                rect = patches.Rectangle(
                    (bbox[0], bbox[1]), bbox[2], bbox[3],
                    linewidth=bbox_thickness, edgecolor=color, facecolor='none'
                )
                ax.add_patch(rect)
                
                # 添加标签
                if show_labels:
                    ax.text(bbox[0], bbox[1] - 5, f"{category_name}", 
                           bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7),
                           fontsize=10, color='white', weight='bold')
            
            # 绘制分割掩码
            if show_segmentation and 'segmentation' in ann and ann['segmentation']:
                for seg in ann['segmentation']:
                    if len(seg) >= 6:  # 至少3个点
                        # 将分割点转换为多边形
                        points = np.array(seg).reshape(-1, 2)
                        polygon = Polygon(points, closed=True, alpha=0.3, 
                                        facecolor=color, edgecolor=color, linewidth=1)
                        ax.add_patch(polygon)
        
        ax.set_xlim(0, image_info['width'])
        ax.set_ylim(image_info['height'], 0)
        ax.axis('off')
        
        # 保存或显示
        if save_path:
            plt.savefig(save_path, bbox_inches='tight', dpi=150)
            print(f"✅ 可视化结果已保存到: {save_path}")
        else:
            plt.show()
        
        plt.close()
    
    def visualize_random_samples(self, num_samples=6, save_dir=None, **kwargs):
        """
        随机可视化多张图片
        
        :param num_samples: 要可视化的图片数量
        :param save_dir: 保存目录，如果为None则显示图片
        :param kwargs: 传递给visualize_image的其他参数
        """
        # 随机选择图片
        image_ids = list(self.image_id_to_info.keys())
        if len(image_ids) < num_samples:
            num_samples = len(image_ids)
            print(f"⚠️  数据集只有 {len(image_ids)} 张图片，将显示全部")
        
        selected_ids = random.sample(image_ids, num_samples)
        
        print(f"🎲 随机可视化 {num_samples} 张图片...")
        
        for i, image_id in enumerate(selected_ids):
            if save_dir:
                image_info = self.image_id_to_info[image_id]
                save_path = os.path.join(save_dir, f"sample_{i+1}_{image_info['file_name']}")
                os.makedirs(save_dir, exist_ok=True)
            else:
                save_path = None
            
            self.visualize_image(image_id=image_id, save_path=save_path, **kwargs)
    
    def visualize_category_samples(self, category_name, num_samples=4, save_dir=None, **kwargs):
        """
        可视化指定类别的样本
        
        :param category_name: 类别名称
        :param num_samples: 每个类别的样本数量
        :param save_dir: 保存目录
        :param kwargs: 传递给visualize_image的其他参数
        """
        # 找到类别ID
        category_id = None
        for cat in self.coco_data['categories']:
            if cat['name'] == category_name:
                category_id = cat['id']
                break
        
        if category_id is None:
            print(f"❌ 类别 '{category_name}' 不存在")
            available_categories = [cat['name'] for cat in self.coco_data['categories']]
            print(f"可用类别: {available_categories}")
            return
        
        # 找到包含该类别的图片
        image_ids_with_category = set()
        for ann in self.coco_data['annotations']:
            if ann['category_id'] == category_id:
                image_ids_with_category.add(ann['image_id'])
        
        if not image_ids_with_category:
            print(f"❌ 没有找到包含类别 '{category_name}' 的图片")
            return
        
        # 随机选择样本
        image_ids = list(image_ids_with_category)
        if len(image_ids) < num_samples:
            num_samples = len(image_ids)
            print(f"⚠️  类别 '{category_name}' 只有 {len(image_ids)} 张图片，将显示全部")
        
        selected_ids = random.sample(image_ids, num_samples)
        
        print(f"🏷️  可视化类别 '{category_name}' 的 {num_samples} 个样本...")
        
        for i, image_id in enumerate(selected_ids):
            if save_dir:
                image_info = self.image_id_to_info[image_id]
                save_path = os.path.join(save_dir, f"{category_name}_sample_{i+1}_{image_info['file_name']}")
                os.makedirs(save_dir, exist_ok=True)
            else:
                save_path = None
            
            self.visualize_image(image_id=image_id, save_path=save_path, **kwargs)
    
    def create_category_distribution_chart(self, save_path=None):
        """
        创建类别分布图表
        
        :param save_path: 保存路径，如果为None则显示图表
        """
        stats = self.get_dataset_statistics()
        
        categories = list(stats['category_stats'].keys())
        counts = [stats['category_stats'][cat]['count'] for cat in categories]
        
        plt.figure(figsize=(12, 8))
        
        # 创建柱状图
        bars = plt.bar(categories, counts, color=[np.array(self.category_colors[self.category_id_to_info[stats['category_stats'][cat]['id']]['id']]) / 255.0 for cat in categories])
        
        plt.title('COCO数据集类别分布', fontsize=16, fontweight='bold')
        plt.xlabel('类别', fontsize=12)
        plt.ylabel('标注数量', fontsize=12)
        plt.xticks(rotation=45, ha='right')
        
        # 在柱子上添加数值
        for bar, count in zip(bars, counts):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(counts)*0.01,
                    str(count), ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, bbox_inches='tight', dpi=150)
            print(f"✅ 类别分布图已保存到: {save_path}")
        else:
            plt.show()
        
        plt.close()
    
    def export_annotation_summary(self, output_file):
        """
        导出标注摘要信息到文件
        
        :param output_file: 输出文件路径
        """
        stats = self.get_dataset_statistics()
        
        summary = {
            "dataset_info": {
                "annotation_file": self.annotation_file,
                "images_directory": self.images_dir,
                "total_images": stats['total_images'],
                "total_annotations": stats['total_annotations'],
                "total_categories": stats['total_categories']
            },
            "image_statistics": stats['image_stats'],
            "category_statistics": stats['category_stats'],
            "categories": [
                {
                    "id": cat['id'],
                    "name": cat['name'],
                    "supercategory": cat.get('supercategory', 'object')
                }
                for cat in self.coco_data['categories']
            ]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 标注摘要已导出到: {output_file}")


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description='COCO数据集可视化工具')
    parser.add_argument('--annotation', '-a', required=True, help='COCO标注JSON文件路径')
    parser.add_argument('--images', '-i', required=True, help='图片文件夹路径')
    parser.add_argument('--output', '-o', help='输出目录（可选）')
    parser.add_argument('--stats', action='store_true', help='显示数据集统计信息')
    parser.add_argument('--random', type=int, default=0, help='随机可视化N张图片')
    parser.add_argument('--category', help='可视化指定类别的样本')
    parser.add_argument('--category-samples', type=int, default=4, help='每个类别的样本数量')
    parser.add_argument('--image-id', type=int, help='可视化指定ID的图片')
    parser.add_argument('--image-name', help='可视化指定文件名的图片')
    parser.add_argument('--chart', action='store_true', help='生成类别分布图表')
    parser.add_argument('--export', help='导出标注摘要到JSON文件')
    
    args = parser.parse_args()
    
    try:
        # 创建可视化器
        visualizer = COCOVisualizer(args.annotation, args.images)
        
        # 显示统计信息
        if args.stats:
            visualizer.print_statistics()
        
        # 随机可视化
        if args.random > 0:
            visualizer.visualize_random_samples(args.random, args.output)
        
        # 类别可视化
        if args.category:
            visualizer.visualize_category_samples(args.category, args.category_samples, args.output)
        
        # 单张图片可视化
        if args.image_id is not None:
            save_path = None
            if args.output:
                os.makedirs(args.output, exist_ok=True)
                save_path = os.path.join(args.output, f"image_{args.image_id}.png")
            visualizer.visualize_image(image_id=args.image_id, save_path=save_path)
        
        if args.image_name:
            save_path = None
            if args.output:
                os.makedirs(args.output, exist_ok=True)
                save_path = os.path.join(args.output, f"image_{args.image_name}.png")
            visualizer.visualize_image(image_filename=args.image_name, save_path=save_path)
        
        # 生成图表
        if args.chart:
            save_path = None
            if args.output:
                os.makedirs(args.output, exist_ok=True)
                save_path = os.path.join(args.output, "category_distribution.png")
            visualizer.create_category_distribution_chart(save_path)
        
        # 导出摘要
        if args.export:
            visualizer.export_annotation_summary(args.export)
        
        # 如果没有指定任何操作，显示统计信息
        if not any([args.stats, args.random, args.category, args.image_id, args.image_name, args.chart, args.export]):
            visualizer.print_statistics()
    
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 如果直接运行，使用示例配置
    if len(os.sys.argv) == 1:
        # 示例配置
        ANNOTATION_FILE = r"E:\datasets\coco_dataset\annotations\train.json"
        IMAGES_DIR = r"E:\datasets\coco_dataset\images\train"
        OUTPUT_DIR = r"E:\datasets\visualization_output"
        
        print("🎨 COCO数据集可视化工具")
        print("=" * 50)
        print("示例用法（请修改路径后使用）:")
        print(f"ANNOTATION_FILE = r'{ANNOTATION_FILE}'")
        print(f"IMAGES_DIR = r'{IMAGES_DIR}'")
        print(f"OUTPUT_DIR = r'{OUTPUT_DIR}'")
        print()
        print("取消注释下面的代码来运行示例:")
        print()
        print("# 创建可视化器")
        print("# visualizer = COCOVisualizer(ANNOTATION_FILE, IMAGES_DIR)")
        print()
        print("# 显示统计信息")
        print("# visualizer.print_statistics()")
        print()
        print("# 随机可视化6张图片")
        print("# visualizer.visualize_random_samples(6, OUTPUT_DIR)")
        print()
        print("# 可视化指定类别的样本")
        print("# visualizer.visualize_category_samples('person', 4, OUTPUT_DIR)")
        print()
        print("# 生成类别分布图表")
        print("# visualizer.create_category_distribution_chart(os.path.join(OUTPUT_DIR, 'distribution.png'))")
        print()
        print("或者使用命令行参数运行:")
        print("python coco_visualizer.py --annotation path/to/annotations.json --images path/to/images --stats")
    else:
        main()