#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CopyText.py - 将标签文件复制到与图像文件名匹配的目标目录

这个脚本用于在两个目录中寻找文件名匹配的文件，并将第一个目录（文本目录）中的文本文件
复制到指定的目标目录。主要用于处理YOLO数据集时，将标签文件与图像文件对应起来。

工作流程：
1. 扫描文本目录中的所有.txt文件
2. 扫描图像目录中的所有图像文件(.png, .jpg, .jpeg, .bmp, .gif)
3. 找出两个目录中文件名匹配的文件（不考虑扩展名）
4. 将匹配的文本文件复制到目标目录
"""
import os
import shutil

def copy_matching_files(text_folder, image_folder, destination_folder):
    # 获取A目录中的所有.txt文件
    text_files = [os.path.splitext(file)[0] for file in os.listdir(text_folder) if file.endswith('.txt')]
    
    # 获取B目录中的所有图像文件
    image_files = [os.path.splitext(file)[0] for file in os.listdir(image_folder) if file.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif'))]
    
    # 找出两个目录中文件名匹配的文件
    matching_files = set(text_files).intersection(set(image_files))
    
    # 如果目标目录不存在，则创建
    if not os.path.exists(destination_folder):
        os.makedirs(destination_folder)
    
    # 将匹配的文本文件复制到目标目录
    for file_name in matching_files:
        src_file_path = os.path.join(text_folder, f"{file_name}.txt")
        dst_file_path = os.path.join(destination_folder, f"{file_name}.txt")
        shutil.copy(src_file_path, dst_file_path)
        print(f"copy: {src_file_path} to {dst_file_path}")

 
 
text_folder = r"D:\WH_WorkFiles\ProjectFile\03RPA\DataSet4\labels\train"  # A目录路径
image_folder = r"D:\WH_WorkFiles\ProjectFile\03RPA\NewDataSet\output2"  # B目录路径
destination_folder = r"D:\WH_WorkFiles\ProjectFile\03RPA\DataSet4\outlabel"  # 目标目录路径


copy_matching_files(text_folder, image_folder, destination_folder)
