import json
import os
import cv2
import shutil
import random
from datetime import datetime
from pathlib import Path


class YoloToCocoDataset:
    """YOLO格式转换为完整COCO数据集结构"""
    
    def __init__(self, yolo_images_dir, yolo_labels_dir, class_names, output_dir, 
                 train_ratio=0.8, val_ratio=0.2, test_ratio=0.0):
        """
        初始化转换器
        
        :param yolo_images_dir: YOLO图片文件夹路径
        :param yolo_labels_dir: YOLO标注文件夹路径
        :param class_names: 类别名称列表
        :param output_dir: 输出COCO数据集根目录
        :param train_ratio: 训练集比例
        :param val_ratio: 验证集比例
        :param test_ratio: 测试集比例
        """
        self.yolo_images_dir = yolo_images_dir
        self.yolo_labels_dir = yolo_labels_dir
        self.class_names = class_names
        self.output_dir = output_dir
        
        # 数据集分割比例
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
        
        # 验证比例
        if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
            raise ValueError("训练集、验证集、测试集比例之和必须等于1.0")
        
        # 验证路径
        if not os.path.exists(yolo_images_dir):
            raise ValueError(f"YOLO图片文件夹不存在: {yolo_images_dir}")
        if not os.path.exists(yolo_labels_dir):
            raise ValueError(f"YOLO标注文件夹不存在: {yolo_labels_dir}")
        
        # 创建COCO数据集目录结构
        self._create_coco_structure()
    
    def _create_coco_structure(self):
        """创建COCO数据集目录结构"""
        self.coco_dirs = {
            'root': self.output_dir,
            'images': os.path.join(self.output_dir, 'images'),
            'annotations': os.path.join(self.output_dir, 'annotations')
        }
        
        # 根据比例创建子目录
        if self.train_ratio > 0:
            self.coco_dirs['train_images'] = os.path.join(self.coco_dirs['images'], 'train')
        if self.val_ratio > 0:
            self.coco_dirs['val_images'] = os.path.join(self.coco_dirs['images'], 'val')
        if self.test_ratio > 0:
            self.coco_dirs['test_images'] = os.path.join(self.coco_dirs['images'], 'test')
        
        # 创建所有目录
        for dir_path in self.coco_dirs.values():
            os.makedirs(dir_path, exist_ok=True)
        
        print(f"创建COCO数据集目录结构: {self.output_dir}")
    
    def _get_image_files(self):
        """获取所有图片文件"""
        image_files = []
        supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        
        for file in os.listdir(self.yolo_images_dir):
            if any(file.lower().endswith(ext) for ext in supported_formats):
                # 检查是否有对应的标注文件
                label_file = os.path.splitext(file)[0] + '.txt'
                label_path = os.path.join(self.yolo_labels_dir, label_file)
                if os.path.exists(label_path):
                    image_files.append(file)
                else:
                    print(f"警告：图片 {file} 没有对应的标注文件，跳过")
        
        return sorted(image_files)
    
    def _split_dataset(self, image_files):
        """分割数据集"""
        random.shuffle(image_files)
        total = len(image_files)
        
        train_end = int(total * self.train_ratio)
        val_end = train_end + int(total * self.val_ratio)
        
        splits = {}
        if self.train_ratio > 0:
            splits['train'] = image_files[:train_end]
        if self.val_ratio > 0:
            splits['val'] = image_files[train_end:val_end]
        if self.test_ratio > 0:
            splits['test'] = image_files[val_end:]
        
        return splits
    
    def _create_coco_annotation_structure(self, split_name):
        """创建COCO标注文件结构"""
        return {
            "info": {
                "description": f"YOLO to COCO conversion - {split_name} set",
                "version": "1.0",
                "year": datetime.now().year,
                "contributor": "Auto-generated",
                "date_created": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            },
            "licenses": [
                {
                    "id": 1,
                    "name": "Unknown",
                    "url": ""
                }
            ],
            "images": [],
            "annotations": [],
            "categories": []
        }
    
    def _add_categories(self, coco_data):
        """添加类别信息"""
        for i, class_name in enumerate(self.class_names):
            coco_data["categories"].append({
                "id": i,
                "name": class_name,
                "supercategory": "object"
            })
    
    def _process_split(self, split_name, image_files, task_type="detection"):
        """处理单个数据集分割"""
        print(f"处理 {split_name} 集: {len(image_files)} 张图片")
        
        # 创建COCO标注结构
        coco_data = self._create_coco_annotation_structure(split_name)
        self._add_categories(coco_data)
        
        # 目标目录
        target_image_dir = self.coco_dirs[f'{split_name}_images']
        annotation_id = 0
        
        for image_id, image_file in enumerate(image_files):
            # 复制图片
            src_image_path = os.path.join(self.yolo_images_dir, image_file)
            dst_image_path = os.path.join(target_image_dir, image_file)
            shutil.copy2(src_image_path, dst_image_path)
            
            # 读取图片尺寸
            img = cv2.imread(src_image_path)
            if img is None:
                print(f"警告：无法读取图片 {image_file}")
                continue
            
            height, width = img.shape[:2]
            
            # 添加图片信息
            coco_data["images"].append({
                "id": image_id,
                "width": width,
                "height": height,
                "file_name": image_file,
                "license": 1,
                "date_captured": ""
            })
            
            # 处理标注
            label_file = os.path.splitext(image_file)[0] + '.txt'
            label_path = os.path.join(self.yolo_labels_dir, label_file)
            
            if os.path.exists(label_path):
                with open(label_path, 'r') as f:
                    for line_num, line in enumerate(f, 1):
                        line = line.strip()
                        if not line:
                            continue
                        
                        parts = line.split()
                        if len(parts) < 5:
                            continue
                        
                        try:
                            class_id = int(parts[0])
                            
                            if task_type == "detection":
                                annotation = self._process_detection_annotation(
                                    parts, class_id, width, height, annotation_id, image_id
                                )
                            else:  # segmentation
                                annotation = self._process_segmentation_annotation(
                                    parts, class_id, width, height, annotation_id, image_id
                                )
                            
                            if annotation:
                                coco_data["annotations"].append(annotation)
                                annotation_id += 1
                                
                        except (ValueError, IndexError) as e:
                            print(f"警告：{label_file} 第{line_num}行格式错误: {e}")
                            continue
        
        # 保存标注文件
        annotation_file = os.path.join(self.coco_dirs['annotations'], f'{split_name}.json')
        with open(annotation_file, 'w', encoding='utf-8') as f:
            json.dump(coco_data, f, indent=2, ensure_ascii=False)
        
        print(f"{split_name} 集处理完成:")
        print(f"  - 图片数量: {len(coco_data['images'])}")
        print(f"  - 标注数量: {len(coco_data['annotations'])}")
        print(f"  - 标注文件: {annotation_file}")
        
        return coco_data
    
    def _process_detection_annotation(self, parts, class_id, width, height, annotation_id, image_id):
        """处理检测标注"""
        if len(parts) < 5:
            return None
        
        cx, cy, w, h = map(float, parts[1:5])
        
        # YOLO格式转COCO格式
        x = (cx - w/2) * width
        y = (cy - h/2) * height
        bbox_width = w * width
        bbox_height = h * height
        
        # 边界检查
        x = max(0, x)
        y = max(0, y)
        bbox_width = min(bbox_width, width - x)
        bbox_height = min(bbox_height, height - y)
        
        if bbox_width <= 0 or bbox_height <= 0:
            return None
        
        return {
            "id": annotation_id,
            "image_id": image_id,
            "category_id": class_id,
            "bbox": [x, y, bbox_width, bbox_height],
            "area": bbox_width * bbox_height,
            "segmentation": [],
            "iscrowd": 0
        }
    
    def _process_segmentation_annotation(self, parts, class_id, width, height, annotation_id, image_id):
        """处理分割标注"""
        if len(parts) < 7:  # 至少需要类别 + 3个点
            return None
        
        coords = list(map(float, parts[1:]))
        if len(coords) % 2 != 0:
            return None
        
        # 转换为像素坐标
        segmentation = []
        for i in range(0, len(coords), 2):
            x = coords[i] * width
            y = coords[i+1] * height
            segmentation.extend([x, y])
        
        # 计算边界框
        x_coords = segmentation[::2]
        y_coords = segmentation[1::2]
        
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        
        bbox_width = x_max - x_min
        bbox_height = y_max - y_min
        
        if bbox_width <= 0 or bbox_height <= 0:
            return None
        
        return {
            "id": annotation_id,
            "image_id": image_id,
            "category_id": class_id,
            "bbox": [x_min, y_min, bbox_width, bbox_height],
            "area": bbox_width * bbox_height,
            "segmentation": [segmentation],
            "iscrowd": 0
        }
    
    def convert(self, task_type="detection", random_seed=42):
        """
        执行完整的YOLO到COCO数据集转换
        
        :param task_type: 任务类型，"detection" 或 "segmentation"
        :param random_seed: 随机种子，用于数据集分割的可重复性
        :return: 转换结果统计
        """
        print("=" * 60)
        print("开始YOLO到COCO数据集转换")
        print("=" * 60)
        
        # 设置随机种子
        random.seed(random_seed)
        
        # 获取所有图片文件
        image_files = self._get_image_files()
        print(f"找到 {len(image_files)} 张有效图片")
        
        if len(image_files) == 0:
            raise ValueError("没有找到有效的图片文件")
        
        # 分割数据集
        splits = self._split_dataset(image_files)
        print(f"数据集分割:")
        for split_name, files in splits.items():
            print(f"  - {split_name}: {len(files)} 张图片")
        
        # 处理每个分割
        results = {}
        for split_name, files in splits.items():
            results[split_name] = self._process_split(split_name, files, task_type)
        
        # 创建数据集信息文件
        self._create_dataset_info(splits, task_type)
        
        print("=" * 60)
        print("COCO数据集转换完成！")
        print(f"输出目录: {self.output_dir}")
        print("目录结构:")
        print(f"  ├── images/")
        for split_name in splits.keys():
            print(f"  │   ├── {split_name}/")
        print(f"  ├── annotations/")
        for split_name in splits.keys():
            print(f"  │   ├── {split_name}.json")
        print(f"  └── dataset_info.json")
        print("=" * 60)
        
        return results
    
    def _create_dataset_info(self, splits, task_type):
        """创建数据集信息文件"""
        dataset_info = {
            "dataset_name": "YOLO_to_COCO_conversion",
            "task_type": task_type,
            "creation_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "classes": {
                "count": len(self.class_names),
                "names": self.class_names
            },
            "splits": {},
            "total_images": sum(len(files) for files in splits.values()),
            "directory_structure": {
                "images": "images/",
                "annotations": "annotations/"
            }
        }
        
        for split_name, files in splits.items():
            dataset_info["splits"][split_name] = {
                "image_count": len(files),
                "annotation_file": f"annotations/{split_name}.json",
                "image_directory": f"images/{split_name}/"
            }
        
        info_file = os.path.join(self.output_dir, 'dataset_info.json')
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(dataset_info, f, indent=2, ensure_ascii=False)


# 便捷函数
def create_coco_dataset(yolo_images_dir, yolo_labels_dir, class_names, output_dir,
                       task_type="detection", train_ratio=0.8, val_ratio=0.2, test_ratio=0.0):
    """
    创建完整COCO数据集的便捷函数
    
    :param yolo_images_dir: YOLO图片文件夹路径
    :param yolo_labels_dir: YOLO标注文件夹路径
    :param class_names: 类别名称列表
    :param output_dir: 输出COCO数据集根目录
    :param task_type: 任务类型，"detection" 或 "segmentation"
    :param train_ratio: 训练集比例
    :param val_ratio: 验证集比例
    :param test_ratio: 测试集比例
    :return: 转换结果
    """
    converter = YoloToCocoDataset(
        yolo_images_dir, yolo_labels_dir, class_names, output_dir,
        train_ratio, val_ratio, test_ratio
    )
    return converter.convert(task_type)


# 使用示例
if __name__ == "__main__":
    # 配置参数
    YOLO_IMAGES_DIR = r"E:\datasets\yolo\images"       # YOLO图片文件夹
    YOLO_LABELS_DIR = r"E:\datasets\yolo\labels"       # YOLO标注文件夹
    OUTPUT_DIR = r"E:\datasets\coco_dataset"           # 输出COCO数据集目录
    CLASS_NAMES = ["person", "car", "bicycle"]         # 类别名称列表
    
    # 数据集分割比例
    TRAIN_RATIO = 0.8   # 训练集80%
    VAL_RATIO = 0.2     # 验证集20%
    TEST_RATIO = 0.0    # 测试集0%（可选）
    
    # 任务类型
    TASK_TYPE = "detection"  # "detection" 或 "segmentation"
    
    try:
        # 方法1: 使用类
        converter = YoloToCocoDataset(
            YOLO_IMAGES_DIR, YOLO_LABELS_DIR, CLASS_NAMES, OUTPUT_DIR,
            TRAIN_RATIO, VAL_RATIO, TEST_RATIO
        )
        results = converter.convert(TASK_TYPE)
        
        # 方法2: 使用便捷函数
        # results = create_coco_dataset(
        #     YOLO_IMAGES_DIR, YOLO_LABELS_DIR, CLASS_NAMES, OUTPUT_DIR,
        #     TASK_TYPE, TRAIN_RATIO, VAL_RATIO, TEST_RATIO
        # )
        
    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("请修改上面的配置参数后运行转换")