# -*- coding: utf-8 -*-
import os
import cv2
import numpy as np

def restore_yolo_labels(crop_label_path, crop_info, original_size):
    """
    将裁剪图片的YOLO格式标签还原为原始图片的标签
    
    参数:
    crop_label_path: 裁剪图片的标签文件路径
    crop_info: (y_start, x_start, crop_height, crop_width) 裁剪信息
    original_size: (orig_width, orig_height) 原始图片尺寸
    
    返回:
    restored_boxes: 还原后的标签列表，每个元素为 [class_id, x, y, w, h]
    """
    restored_boxes = []
    y_start, x_start, crop_height, crop_width = crop_info
    orig_width, orig_height = original_size
    
    if os.path.exists(crop_label_path):
        with open(crop_label_path, 'r') as f:
            for line in f.readlines():
                class_id, x, y, w, h = map(float, line.strip().split())
                
                # 将YOLO格式转换为裁剪图上的像素坐标
                x_center_px = int(x * crop_width)
                y_center_px = int(y * crop_height)
                width_px = int(w * crop_width)
                height_px = int(h * crop_height)
                
                # 转换回原始图像上的像素坐标
                orig_x_center = x_center_px + x_start
                orig_y_center = y_center_px + y_start
                
                # 转换为原始图像上的YOLO格式
                restored_x = orig_x_center / orig_width
                restored_y = orig_y_center / orig_height
                restored_w = width_px / orig_width
                restored_h = height_px / orig_height
                
                restored_boxes.append([class_id, restored_x, restored_y, restored_w, restored_h])
    
    return restored_boxes

def save_yolo_labels(boxes, output_path):
    """保存YOLO格式标签"""
    with open(output_path, 'w') as f:
        for box in boxes:
            class_id, x, y, w, h = box
            line = f"{int(class_id)} {x:.6f} {y:.6f} {w:.6f} {h:.6f}\n"
            f.write(line)

def draw_boxes(image, boxes, class_names=None):
    """在图像上绘制边界框"""
    height, width = image.shape[:2]
    for box in boxes:
        class_id, x, y, w, h = box
        
        # 将YOLO格式转换为像素坐标
        x_center = int(x * width)
        y_center = int(y * height)
        box_width = int(w * width)
        box_height = int(h * height)
        
        x1 = int(x_center - box_width/2)
        y1 = int(y_center - box_height/2)
        x2 = int(x_center + box_width/2)
        y2 = int(y_center + box_height/2)
        
        # 确保坐标在图像范围内
        x1 = max(0, min(width-1, x1))
        y1 = max(0, min(height-1, y1))
        x2 = max(0, min(width-1, x2))
        y2 = max(0, min(height-1, y2))
        
        # 绘制边界框
        color = (0, 255, 0)  # 绿色
        cv2.rectangle(image, (x1, y1), (x2, y2), color, 2)
        
        if class_names and int(class_id) < len(class_names):
            label = class_names[int(class_id)]
            cv2.putText(image, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    return image

def process_restore_labels(crop_label_dir, orig_image_dir, output_base_dir, class_names=None):
    """
    处理标签还原
    
    参数:
    crop_label_dir: 裁剪图片的标签目录
    orig_image_dir: 原始图片目录
    output_base_dir: 输出目录
    class_names: 类别名称列表
    """
    # Halcon裁剪信息
    y2 = 585+1
    x2 = 528+1
    crop_info = (104, 14, y2-104, x2-14)  # (y_start, x_start, height, width)
    
    # 创建输出目录
    output_label_dir = os.path.join(output_base_dir, 'labels')
    visual_dir = os.path.join(output_base_dir, 'visual')
    os.makedirs(output_label_dir, exist_ok=True)
    os.makedirs(visual_dir, exist_ok=True)
    
    print(f'处理目录:')
    print(f'裁剪标签目录: {crop_label_dir}')
    print(f'原始图像目录: {orig_image_dir}')
    print(f'标签输出目录: {output_label_dir}')
    print(f'可视化目录: {visual_dir}')
    
    # 处理每个标签文件
    for label_name in os.listdir(crop_label_dir):
        if label_name.endswith('.txt'):
            # 获取对应的图像名
            image_name = os.path.splitext(label_name)[0] + '.bmp'
            orig_image_path = os.path.join(orig_image_dir, image_name)
            
            # 检查原始图像是否存在
            if not os.path.exists(orig_image_path):
                print(f'警告: 找不到原始图像 {orig_image_path}')
                continue
            
            # 读取原始图像获取尺寸
            orig_image = cv2.imread(orig_image_path)
            if orig_image is None:
                print(f'警告: 无法读取图像 {orig_image_path}')
                continue
            
            original_size = (orig_image.shape[1], orig_image.shape[0])
            
            # 获取标签路径
            crop_label_path = os.path.join(crop_label_dir, label_name)
            output_label_path = os.path.join(output_label_dir, label_name)
            visual_path = os.path.join(visual_dir, f'visual_{image_name}')
            
            # 还原标签
            restored_boxes = restore_yolo_labels(crop_label_path, crop_info, original_size)
            
            # 保存还原的标签
            save_yolo_labels(restored_boxes, output_label_path)
            print(f'保存还原的标签: {output_label_path}')
            
            # 绘制可视化结果
            if restored_boxes:
                visualized_image = draw_boxes(orig_image.copy(), restored_boxes, class_names)
                cv2.imwrite(visual_path, visualized_image)
                print(f'保存可视化图像: {visual_path} (包含 {len(restored_boxes)} 个标注)')
            else:
                print(f'警告: {label_name} 没有标注')

if __name__ == '__main__':
    # 输入目录
    crop_label_dir = r'C:\Users\<USER>\Desktop\label'  # 裁剪图片的标签目录
    orig_image_dir = r'C:\Users\<USER>\Desktop\source'  # 原始图片目录
    output_base_dir = r'C:\Users\<USER>\Desktop\output'  # 输出目录
    
    # 类别列表
    class_names = ['kerf']
    
    # 处理标签还原
    process_restore_labels(crop_label_dir, orig_image_dir, output_base_dir, class_names) 