#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
YOLO2CroppedImage.py - 将标注好的YOLO数据转换成裁剪后对应图像的数据

这个脚本用于将YOLO格式的标注数据转换成裁剪后对应图像的数据。
它使用Halcon样式的裁剪坐标 [row1, col1, row2, col2] 裁剪图像，
并将YOLO格式的标注映射到裁剪后的图像坐标系中。

同时，它可以读取原始图像对应的文本标注文件，并将文本标注映射到裁剪后的图像。
"""
import os
import cv2
import numpy as np
import argparse
import json
from pathlib import Path

# ===================== 参数配置区域 - 直接修改这里的参数 ===================== #
# 图像和标签目录
IMAGES_DIR = r"C:\Users\<USER>\Desktop\image" # 图像目录
LABELS_DIR = r"C:\Users\<USER>\Desktop\label" # YOLO标注文件目录

# Halcon裁剪坐标 [row1, col1, row2, col2]
CROP_COORDS = [300, 15, 400, 470]  # 直接使用列表，不需要字符串

# 输出目录
OUTPUT_DIR = r"C:\Users\<USER>\Desktop\output"  # 输出目录

# 可选参数
TEXT_FILE = r""  # 文本标注文件路径，如果有的话
VISUAL = True    # 是否生成可视化结果

# 已裁剪图像参数
ALREADY_CROPPED = True  # 图像是否已经裁剪好了
ORIGINAL_SIZE = (1024, 768)  # 原始图像尺寸 (width, height)，当ALREADY_CROPPED=True时需要
# =========================================================================== #

def read_yolo_labels(label_path, original_size, crop_coords):
    """
    读取YOLO格式的标签文件并转换为裁剪后图像中的坐标
    
    参数:
        label_path: YOLO标签文件路径
        original_size: 原始图像尺寸，格式为(width, height)
        crop_coords: Halcon裁剪坐标，格式为[row1, col1, row2, col2]
        
    返回:
        boxes: 转换后的边界框列表，格式为[[class_id, x, y, w, h], ...]
    """
    boxes = []
    if not os.path.exists(label_path):
        print(f"警告: 标签文件不存在 {label_path}")
        return boxes
    
    orig_w, orig_h = original_size
    row1, col1, row2, col2 = crop_coords
    
    # 计算裁剪区域的宽高
    crop_height = row2 - row1
    crop_width = col2 - col1
    
    try:
        with open(label_path, 'r') as f:
            for line in f.readlines():
                parts = line.strip().split()
                if len(parts) >= 5:  # 确保至少有5个元素(class_id, x, y, w, h)
                    class_id = int(parts[0])
                    x = float(parts[1])
                    y = float(parts[2])
                    w = float(parts[3])
                    h = float(parts[4])
                    
                    # 将YOLO格式(归一化坐标)转换为原始图像上的像素坐标
                    x_center_px = int(x * orig_w)
                    y_center_px = int(y * orig_h)
                    width_px = int(w * orig_w)
                    height_px = int(h * orig_h)
                    
                    # 计算边界框在原始图像中的坐标
                    x1 = x_center_px - width_px // 2  # 左上角x
                    y1 = y_center_px - height_px // 2  # 左上角y
                    x2 = x1 + width_px  # 右下角x
                    y2 = y1 + height_px  # 右下角y
                    
                    # 将坐标转换到裁剪图像中
                    x1_crop = x1 - col1
                    y1_crop = y1 - row1
                    x2_crop = x2 - col1
                    y2_crop = y2 - row1
                    
                    # 检查边界框是否在裁剪区域内
                    if (x2_crop > 0 and x1_crop < crop_width and 
                        y2_crop > 0 and y1_crop < crop_height):
                        # 确保边界框在裁剪区域内
                        x1_crop = max(0, x1_crop)
                        y1_crop = max(0, y1_crop)
                        x2_crop = min(crop_width, x2_crop)
                        y2_crop = min(crop_height, y2_crop)
                        
                        # 计算裁剪图像中的宽度和高度
                        new_width = x2_crop - x1_crop
                        new_height = y2_crop - y1_crop
                        
                        # 只有当边界框足够大时才添加
                        if new_width > 1 and new_height > 1:
                            # 计算新的中心点
                            new_x_center = x1_crop + new_width / 2
                            new_y_center = y1_crop + new_height / 2
                            
                            # 转换回YOLO格式(归一化坐标)
                            new_x = new_x_center / crop_width
                            new_y = new_y_center / crop_height
                            new_w = new_width / crop_width
                            new_h = new_height / crop_height
                            
                            boxes.append([class_id, new_x, new_y, new_w, new_h])
    except Exception as e:
        print(f"处理标签文件时出错 {label_path}: {e}")
    
    return boxes

def save_yolo_labels(boxes, output_path):
    """
    保存YOLO格式的标签文件
    
    参数:
        boxes: 边界框列表，格式为[[class_id, x, y, w, h], ...]
        output_path: 输出文件路径
    """
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    with open(output_path, 'w') as f:
        for box in boxes:
            class_id, x, y, w, h = box
            # 确保class_id是整数
            line = f"{int(class_id)} {x:.6f} {y:.6f} {w:.6f} {h:.6f}\n"
            f.write(line)

def crop_image(image_path, crop_coords, output_path=None):
    """
    使用Halcon格式坐标裁剪图像
    
    参数:
        image_path: 原始图像路径
        crop_coords: Halcon裁剪坐标，格式为[row1, col1, row2, col2]
        output_path: 输出图像路径，如果为None则不保存
        
    返回:
        cropped_image: 裁剪后的图像
        original_size: 原始图像尺寸，格式为(width, height)
    """
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"错误: 无法读取图像 {image_path}")
        return None, None
    
    # 获取原始图像尺寸
    height, width = image.shape[:2]
    original_size = (width, height)
    
    # 解析裁剪坐标
    row1, col1, row2, col2 = crop_coords
    
    # 确保坐标在图像范围内
    row1 = max(0, int(row1))
    col1 = max(0, int(col1))
    row2 = min(height, int(row2))
    col2 = min(width, int(col2))
    
    # 裁剪图像 - OpenCV使用[y, x]顺序
    cropped_image = image[row1:row2, col1:col2]
    
    # 如果指定了输出路径，保存裁剪后的图像
    if output_path:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        cv2.imwrite(output_path, cropped_image)
        print(f"已保存裁剪图像到: {output_path}")
    
    return cropped_image, original_size

def map_text_annotations(text_file_path, image_name, output_path=None):
    """
    将文本标注映射到裁剪后的图像
    
    参数:
        text_file_path: 原始文本标注文件路径
        image_name: 图像文件名，用于在标注文件中匹配
        output_path: 输出文本文件路径，如果为None则返回文本
        
    返回:
        text: 映射后的文本标注
    """
    text = ""
    
    if not os.path.exists(text_file_path):
        print(f"警告: 文本标注文件不存在 {text_file_path}")
        return text
    
    # 从文本文件中获取对应图像的标注
    try:
        image_base_name = os.path.splitext(os.path.basename(image_name))[0]
        with open(text_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                # 检查行是否包含图像名称
                if image_base_name in line:
                    # 提取文本标注部分
                    parts = line.split()
                    if len(parts) > 1:
                        # 假设格式为"图像名称 标注文本"
                        text = ' '.join(parts[1:])
                        break
    except Exception as e:
        print(f"处理文本标注文件时出错 {text_file_path}: {e}")
    
    # 如果指定了输出路径，保存映射后的文本
    if output_path and text:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f"{os.path.basename(image_name)} {text}\n")
        print(f"已保存文本标注到: {output_path}")
    
    return text

def process_dataset(images_dir, labels_dir, crop_coords, text_file_path, output_dir, visual=False):
    """
    处理整个数据集，将YOLO标注转换为裁剪后图像上的标注
    
    参数:
        images_dir: 原始图像目录
        labels_dir: YOLO标注文件目录
        crop_coords: Halcon裁剪坐标，格式为[row1, col1, row2, col2]
        text_file_path: 原始文本标注文件路径
        output_dir: 输出目录
        visual: 是否生成可视化结果
    """
    # 创建输出目录
    output_images_dir = os.path.join(output_dir, 'images')
    output_labels_dir = os.path.join(output_dir, 'labels')
    output_text_dir = os.path.join(output_dir, 'texts')
    
    if visual:
        output_visual_dir = os.path.join(output_dir, 'visual')
        os.makedirs(output_visual_dir, exist_ok=True)
    
    os.makedirs(output_images_dir, exist_ok=True)
    os.makedirs(output_labels_dir, exist_ok=True)
    os.makedirs(output_text_dir, exist_ok=True)
    
    # 获取所有图像文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend([f for f in os.listdir(images_dir) if f.lower().endswith(ext)])
    
    print(f"找到 {len(image_files)} 个图像文件")
    
    # 创建汇总文本文件
    all_text_path = os.path.join(output_dir, 'all_texts.txt')
    all_text_file = open(all_text_path, 'w', encoding='utf-8')
    
    # 处理每个图像
    processed_count = 0
    for image_name in image_files:
        image_path = os.path.join(images_dir, image_name)
        label_path = os.path.join(labels_dir, os.path.splitext(image_name)[0] + '.txt')
        
        # 输出路径
        output_image_path = os.path.join(output_images_dir, image_name)
        output_label_path = os.path.join(output_labels_dir, os.path.splitext(image_name)[0] + '.txt')
        output_text_path = os.path.join(output_text_dir, os.path.splitext(image_name)[0] + '.txt')
        
        # 裁剪图像
        cropped_image, original_size = crop_image(image_path, crop_coords, output_image_path)
        if cropped_image is None:
            continue
        
        # 转换标签
        boxes = read_yolo_labels(label_path, original_size, crop_coords)
        if boxes:
            save_yolo_labels(boxes, output_label_path)
            print(f"已保存转换后的标签到: {output_label_path} (包含 {len(boxes)} 个标注)")
        else:
            print(f"警告: {image_name} 没有标注在裁剪区域内")
        
        # 映射文本标注
        text = map_text_annotations(text_file_path, image_name, output_text_path)
        if text:
            all_text_file.write(f"{os.path.basename(output_image_path)} {text}\n")
            print(f"已映射文本标注: {text}")
        
        # 生成可视化结果
        if visual and boxes:
            output_visual_path = os.path.join(output_visual_dir, f"visual_{image_name}")
            visualize_image = cropped_image.copy()
            
            # 绘制边界框
            height, width = visualize_image.shape[:2]
            for box in boxes:
                class_id, x, y, w, h = box
                
                # 将YOLO格式转换为OpenCV格式
                x_center = int(x * width)
                y_center = int(y * height)
                box_width = int(w * width)
                box_height = int(h * height)
                
                # 计算边界框的四个角点
                x1 = int(x_center - box_width/2)
                y1 = int(y_center - box_height/2)
                x2 = int(x_center + box_width/2)
                y2 = int(y_center + box_height/2)
                
                # 确保角点在图像边界内
                x1 = max(0, x1)
                y1 = max(0, y1)
                x2 = min(width, x2)
                y2 = min(height, y2)
                
                # 绘制边界框
                color = (0, 255, 0)  # 绿色
                cv2.rectangle(visualize_image, (x1, y1), (x2, y2), color, 2)
                
                # 显示类别ID
                cv2.putText(visualize_image, str(int(class_id)), (x1, y1-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            # 显示文本标注
            if text:
                cv2.putText(visualize_image, text, (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            cv2.imwrite(output_visual_path, visualize_image)
            print(f"已保存可视化结果到: {output_visual_path}")
        
        processed_count += 1
        print(f"已处理 {processed_count}/{len(image_files)} 图像: {image_name}")
        print("---------------------------------------------------")
    
    # 关闭汇总文本文件
    all_text_file.close()
    print(f"已完成所有处理，结果保存在: {output_dir}")
    print(f"- 裁剪图像: {output_images_dir}")
    print(f"- 转换标签: {output_labels_dir}")
    print(f"- 映射文本: {output_text_dir}")
    print(f"- 汇总文本: {all_text_path}")
    if visual:
        print(f"- 可视化结果: {output_visual_dir}")
    print(f"总共处理了 {processed_count} 个图像")

def process_cropped_dataset(images_dir, labels_dir, crop_coords, original_size, text_file_path, output_dir, visual=False):
    """
    处理已裁剪的数据集，将YOLO标注转换为裁剪后图像上的标注
    
    参数:
        images_dir: 已裁剪的图像目录
        labels_dir: 原始YOLO标注文件目录
        crop_coords: Halcon裁剪坐标，格式为[row1, col1, row2, col2]
        original_size: 原始图像尺寸，格式为(width, height)
        text_file_path: 原始文本标注文件路径
        output_dir: 输出目录
        visual: 是否生成可视化结果
    """
    # 创建输出目录
    output_labels_dir = os.path.join(output_dir, 'labels')
    output_text_dir = os.path.join(output_dir, 'texts')
    
    if visual:
        output_visual_dir = os.path.join(output_dir, 'visual')
        os.makedirs(output_visual_dir, exist_ok=True)
    
    os.makedirs(output_labels_dir, exist_ok=True)
    os.makedirs(output_text_dir, exist_ok=True)
    
    # 获取所有图像文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend([f for f in os.listdir(images_dir) if f.lower().endswith(ext)])
    
    print(f"找到 {len(image_files)} 个已裁剪的图像文件")
    
    # 创建汇总文本文件
    all_text_path = os.path.join(output_dir, 'all_texts.txt')
    all_text_file = open(all_text_path, 'w', encoding='utf-8')
    
    # 处理每个图像
    processed_count = 0
    for image_name in image_files:
        image_path = os.path.join(images_dir, image_name)
        label_path = os.path.join(labels_dir, os.path.splitext(image_name)[0] + '.txt')
        
        # 输出路径
        output_label_path = os.path.join(output_labels_dir, os.path.splitext(image_name)[0] + '.txt')
        output_text_path = os.path.join(output_text_dir, os.path.splitext(image_name)[0] + '.txt')
        
        # 读取已裁剪的图像用于可视化和获取尺寸
        cropped_image = cv2.imread(image_path)
        if cropped_image is None:
            print(f"错误: 无法读取已裁剪图像 {image_path}")
            continue
        
        # 转换标签
        boxes = read_yolo_labels(label_path, original_size, crop_coords)
        if boxes:
            save_yolo_labels(boxes, output_label_path)
            print(f"已保存转换后的标签到: {output_label_path} (包含 {len(boxes)} 个标注)")
        else:
            print(f"警告: {image_name} 没有标注在裁剪区域内")
        
        # 映射文本标注
        text = map_text_annotations(text_file_path, image_name, output_text_path)
        if text:
            all_text_file.write(f"{os.path.basename(image_path)} {text}\n")
            print(f"已映射文本标注: {text}")
        
        # 生成可视化结果
        if visual and boxes:
            output_visual_path = os.path.join(output_visual_dir, f"visual_{image_name}")
            visualize_image = cropped_image.copy()
            
            # 绘制边界框
            height, width = visualize_image.shape[:2]
            for box in boxes:
                class_id, x, y, w, h = box
                
                # 将YOLO格式转换为OpenCV格式
                x_center = int(x * width)
                y_center = int(y * height)
                box_width = int(w * width)
                box_height = int(h * height)
                
                # 计算边界框的四个角点
                x1 = int(x_center - box_width/2)
                y1 = int(y_center - box_height/2)
                x2 = int(x_center + box_width/2)
                y2 = int(y_center + box_height/2)
                
                # 确保角点在图像边界内
                x1 = max(0, x1)
                y1 = max(0, y1)
                x2 = min(width, x2)
                y2 = min(height, y2)
                
                # 绘制边界框
                color = (0, 255, 0)  # 绿色
                cv2.rectangle(visualize_image, (x1, y1), (x2, y2), color, 2)
                
                # 显示类别ID
                cv2.putText(visualize_image, str(int(class_id)), (x1, y1-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            # 显示文本标注
            if text:
                cv2.putText(visualize_image, text, (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            cv2.imwrite(output_visual_path, visualize_image)
            print(f"已保存可视化结果到: {output_visual_path}")
        
        processed_count += 1
        print(f"已处理 {processed_count}/{len(image_files)} 图像: {image_name}")
        print("---------------------------------------------------")
    
    # 关闭汇总文本文件
    all_text_file.close()
    print(f"已完成所有处理，结果保存在: {output_dir}")
    print(f"- 转换标签: {output_labels_dir}")
    print(f"- 映射文本: {output_text_dir}")
    print(f"- 汇总文本: {all_text_path}")
    if visual:
        print(f"- 可视化结果: {output_visual_dir}")
    print(f"总共处理了 {processed_count} 个图像")

def parse_crop_coords(crop_coords_str):
    """
    解析裁剪坐标字符串
    
    参数:
        crop_coords_str: 裁剪坐标字符串，格式为"row1,col1,row2,col2"
        
    返回:
        crop_coords: 裁剪坐标列表，格式为[row1, col1, row2, col2]
    """
    try:
        coords = list(map(int, crop_coords_str.split(',')))
        if len(coords) != 4:
            raise ValueError("裁剪坐标必须包含4个值: row1,col1,row2,col2")
        return coords
    except Exception as e:
        raise ValueError(f"无法解析裁剪坐标: {e}")

def run_with_parameters():
    """使用脚本开头定义的参数运行程序"""
    try:
        # 检查必要参数
        if not os.path.exists(IMAGES_DIR):
            raise FileNotFoundError(f"图像目录不存在: {IMAGES_DIR}")
        
        if not os.path.exists(LABELS_DIR):
            raise FileNotFoundError(f"标注文件目录不存在: {LABELS_DIR}")
        
        if TEXT_FILE and not os.path.exists(TEXT_FILE):
            print(f"警告: 文本标注文件不存在: {TEXT_FILE}")
        
        # 创建输出目录
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        
        # 根据是否已裁剪选择处理函数
        if ALREADY_CROPPED:
            # 使用已裁剪图像的处理函数
            process_cropped_dataset(
                IMAGES_DIR,
                LABELS_DIR,
                CROP_COORDS,
                ORIGINAL_SIZE,
                TEXT_FILE,
                OUTPUT_DIR,
                VISUAL
            )
        else:
            # 使用原始处理函数
            process_dataset(
                IMAGES_DIR,
                LABELS_DIR,
                CROP_COORDS,
                TEXT_FILE,
                OUTPUT_DIR,
                VISUAL
            )
        
        print("转换完成！")
        
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0

def main():
    """主函数，处理命令行参数并执行转换"""
    parser = argparse.ArgumentParser(description="将YOLO标注转换为裁剪图像上的标注")
    
    # 必需参数
    parser.add_argument('--images_dir', required=True, help="图像目录(已裁剪或原始图像)")
    parser.add_argument('--labels_dir', required=True, help="YOLO标注文件目录")
    parser.add_argument('--crop_coords', required=True, help="Halcon裁剪坐标，格式为row1,col1,row2,col2")
    parser.add_argument('--output_dir', required=True, help="输出目录")
    
    # 可选参数
    parser.add_argument('--text_file', help="原始文本标注文件路径")
    parser.add_argument('--visual', action='store_true', help="是否生成可视化结果")
    parser.add_argument('--already_cropped', action='store_true', help="图像是否已经裁剪好")
    parser.add_argument('--original_size', help="原始图像尺寸，格式为width,height，仅当--already_cropped指定时需要")
    
    args = parser.parse_args()
    
    try:
        # 解析裁剪坐标
        crop_coords = parse_crop_coords(args.crop_coords)
        
        # 检查目录是否存在
        if not os.path.exists(args.images_dir):
            raise FileNotFoundError(f"图像目录不存在: {args.images_dir}")
        
        if not os.path.exists(args.labels_dir):
            raise FileNotFoundError(f"标注文件目录不存在: {args.labels_dir}")
        
        if args.text_file and not os.path.exists(args.text_file):
            print(f"警告: 文本标注文件不存在: {args.text_file}")
        
        # 创建输出目录
        os.makedirs(args.output_dir, exist_ok=True)
        
        # 如果图像已经裁剪好，检查是否提供了原始图像尺寸
        if args.already_cropped:
            if not args.original_size:
                raise ValueError("当指定--already_cropped时，必须提供原始图像尺寸(--original_size)")
            
            # 解析原始图像尺寸
            try:
                orig_width, orig_height = map(int, args.original_size.split(','))
                original_size = (orig_width, orig_height)
            except Exception as e:
                raise ValueError(f"无法解析原始图像尺寸: {e}, 请使用格式'width,height'")
            
            # 调用处理已裁剪图像的函数
            process_cropped_dataset(
                args.images_dir,
                args.labels_dir,
                crop_coords,
                original_size,
                args.text_file,
                args.output_dir,
                args.visual
            )
        else:
            # 执行原始处理流程(裁剪图像并转换标注)
            process_dataset(
                args.images_dir,
                args.labels_dir,
                crop_coords,
                args.text_file,
                args.output_dir,
                args.visual
            )
        
        print("转换完成！")
        
    except Exception as e:
        print(f"错误: {e}")
        parser.print_help()
        return 1
    
    return 0

if __name__ == "__main__":
    # 优先使用直接在脚本中定义的参数运行
    run_with_parameters()
    
    # 如果需要使用命令行参数，可以取消下面一行的注释
    # main() 