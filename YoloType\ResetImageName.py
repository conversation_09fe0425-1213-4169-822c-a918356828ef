#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ResetImageName.py - 重命名并合并图像文件

这个脚本用于将一个或多个目录中的图像文件重命名为基于时间戳的名称，并复制到目标目录。
每个图像文件会被重命名为形如"YYYYMMDD_HHMMSS_ffffff.ext"的格式，确保文件名唯一。

主要功能：
1. 递归遍历源目录下的所有图像文件
2. 使用当前时间戳（精确到微秒）为每个文件创建唯一的文件名
3. 将重命名后的文件复制到指定的目标目录
4. 支持多种图像格式：.jpg, .jpeg, .png, .bmp等

适用于需要整理、合并和标准化命名图像文件的场景，如数据集准备或文件整理。
"""
import os
import shutil
from pathlib import Path
from datetime import datetime

def rename_and_merge_images(source_dir, target_dir, file_extensions=('.jpg', '.jpeg', '.png','.bmp')):
   
    # 创建目标文件夹（如果不存在）
    os.makedirs(target_dir, exist_ok=True)
    
    # 初始统计处理的文件数量
    total_files = 0
    processed_dirs = set()
    
    # 确保源文件夹存在
    if not os.path.exists(source_dir):
        print(f"  {source_dir} no exit")
        return
        
    # 遍历源文件夹中的所有文件和子文件夹
    for root, dirs, files in os.walk(source_dir):
        # 记录当前处理的目录
        relative_path = os.path.relpath(root, source_dir)
        if relative_path == '.':
            print(f"\n process main dir: {source_dir}")
        else:
            if root not in processed_dirs:
                print(f"\n process sub dir: {root}")
                processed_dirs.add(root)
        
        # 处理当前目录中的所有文件
        for file in files:
            # 检查文件扩展名
            if file.lower().endswith(file_extensions):
                # 构建源文件的完整路径
                source_file = os.path.join(root, file)
                
                # 获取文件扩展名
                _, ext = os.path.splitext(file)
                
                # 获取当前时间（精确到微秒）
                current_time = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                
                # 创建新的文件名
                new_filename = f"{current_time}{ext}"
                target_file = os.path.join(target_dir, new_filename)
                
                # 复制并重命名文件
                shutil.copy2(source_file, target_file)
                print(f"rename: {source_file} -> {new_filename}")
                total_files += 1

    print(f"\n done")
    print(f"total process {total_files} files")
    print(f"all files copy to: {target_dir}")

if __name__ == "__main__":
    # 指定源文件夹路径
    source_folder = r"D:\Desktop\new\ls"
    
    # 在源文件夹同级创建merged_images文件夹
    # target_folder = os.path.join(os.path.dirname(source_folder), "merged_images")
    target_folder = os.path.join(r"D:\Desktop\new", "merged_images")
    # 执行重命名和合并操作
    rename_and_merge_images(source_folder, target_folder)
